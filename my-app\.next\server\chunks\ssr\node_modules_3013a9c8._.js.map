{"version": 3, "sources": [], "sections": [{"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/framer-motion/dist/es/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AnimatePresence = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimatePresence() from the server but AnimatePresence is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"AnimatePresence\",\n);\nexport const AnimateSharedLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimateSharedLayout() from the server but AnimateSharedLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"AnimateSharedLayout\",\n);\nexport const AsyncMotionValueAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call AsyncMotionValueAnimation() from the server but AsyncMotionValueAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"AsyncMotionValueAnimation\",\n);\nexport const DOMKeyframesResolver = registerClientReference(\n    function() { throw new Error(\"Attempted to call DOMKeyframesResolver() from the server but DOMKeyframesResolver is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"DOMKeyframesResolver\",\n);\nexport const DeprecatedLayoutGroupContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call DeprecatedLayoutGroupContext() from the server but DeprecatedLayoutGroupContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"DeprecatedLayoutGroupContext\",\n);\nexport const DragControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call DragControls() from the server but DragControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"DragControls\",\n);\nexport const FlatTree = registerClientReference(\n    function() { throw new Error(\"Attempted to call FlatTree() from the server but FlatTree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"FlatTree\",\n);\nexport const GroupAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call GroupAnimation() from the server but GroupAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"GroupAnimation\",\n);\nexport const GroupAnimationWithThen = registerClientReference(\n    function() { throw new Error(\"Attempted to call GroupAnimationWithThen() from the server but GroupAnimationWithThen is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"GroupAnimationWithThen\",\n);\nexport const JSAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call JSAnimation() from the server but JSAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"JSAnimation\",\n);\nexport const KeyframeResolver = registerClientReference(\n    function() { throw new Error(\"Attempted to call KeyframeResolver() from the server but KeyframeResolver is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"KeyframeResolver\",\n);\nexport const LayoutGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutGroup() from the server but LayoutGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"LayoutGroup\",\n);\nexport const LayoutGroupContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutGroupContext() from the server but LayoutGroupContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"LayoutGroupContext\",\n);\nexport const LazyMotion = registerClientReference(\n    function() { throw new Error(\"Attempted to call LazyMotion() from the server but LazyMotion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"LazyMotion\",\n);\nexport const MotionConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionConfig() from the server but MotionConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"MotionConfig\",\n);\nexport const MotionConfigContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionConfigContext() from the server but MotionConfigContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"MotionConfigContext\",\n);\nexport const MotionContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionContext() from the server but MotionContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"MotionContext\",\n);\nexport const MotionGlobalConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionGlobalConfig() from the server but MotionGlobalConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"MotionGlobalConfig\",\n);\nexport const MotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionValue() from the server but MotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"MotionValue\",\n);\nexport const NativeAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call NativeAnimation() from the server but NativeAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"NativeAnimation\",\n);\nexport const NativeAnimationExtended = registerClientReference(\n    function() { throw new Error(\"Attempted to call NativeAnimationExtended() from the server but NativeAnimationExtended is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"NativeAnimationExtended\",\n);\nexport const NativeAnimationWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call NativeAnimationWrapper() from the server but NativeAnimationWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"NativeAnimationWrapper\",\n);\nexport const PresenceContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call PresenceContext() from the server but PresenceContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"PresenceContext\",\n);\nexport const Reorder = registerClientReference(\n    function() { throw new Error(\"Attempted to call Reorder() from the server but Reorder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"Reorder\",\n);\nexport const SubscriptionManager = registerClientReference(\n    function() { throw new Error(\"Attempted to call SubscriptionManager() from the server but SubscriptionManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"SubscriptionManager\",\n);\nexport const SwitchLayoutGroupContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call SwitchLayoutGroupContext() from the server but SwitchLayoutGroupContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"SwitchLayoutGroupContext\",\n);\nexport const ViewTransitionBuilder = registerClientReference(\n    function() { throw new Error(\"Attempted to call ViewTransitionBuilder() from the server but ViewTransitionBuilder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"ViewTransitionBuilder\",\n);\nexport const VisualElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call VisualElement() from the server but VisualElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"VisualElement\",\n);\nexport const WillChangeMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call WillChangeMotionValue() from the server but WillChangeMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"WillChangeMotionValue\",\n);\nexport const acceleratedValues = registerClientReference(\n    function() { throw new Error(\"Attempted to call acceleratedValues() from the server but acceleratedValues is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"acceleratedValues\",\n);\nexport const activeAnimations = registerClientReference(\n    function() { throw new Error(\"Attempted to call activeAnimations() from the server but activeAnimations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"activeAnimations\",\n);\nexport const addAttrValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call addAttrValue() from the server but addAttrValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"addAttrValue\",\n);\nexport const addPointerEvent = registerClientReference(\n    function() { throw new Error(\"Attempted to call addPointerEvent() from the server but addPointerEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"addPointerEvent\",\n);\nexport const addPointerInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call addPointerInfo() from the server but addPointerInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"addPointerInfo\",\n);\nexport const addScaleCorrector = registerClientReference(\n    function() { throw new Error(\"Attempted to call addScaleCorrector() from the server but addScaleCorrector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"addScaleCorrector\",\n);\nexport const addStyleValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call addStyleValue() from the server but addStyleValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"addStyleValue\",\n);\nexport const addUniqueItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call addUniqueItem() from the server but addUniqueItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"addUniqueItem\",\n);\nexport const alpha = registerClientReference(\n    function() { throw new Error(\"Attempted to call alpha() from the server but alpha is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"alpha\",\n);\nexport const analyseComplexValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call analyseComplexValue() from the server but analyseComplexValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"analyseComplexValue\",\n);\nexport const animate = registerClientReference(\n    function() { throw new Error(\"Attempted to call animate() from the server but animate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animate\",\n);\nexport const animateMini = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateMini() from the server but animateMini is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animateMini\",\n);\nexport const animateValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateValue() from the server but animateValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animateValue\",\n);\nexport const animateView = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateView() from the server but animateView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animateView\",\n);\nexport const animateVisualElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateVisualElement() from the server but animateVisualElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animateVisualElement\",\n);\nexport const animationControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call animationControls() from the server but animationControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animationControls\",\n);\nexport const animationMapKey = registerClientReference(\n    function() { throw new Error(\"Attempted to call animationMapKey() from the server but animationMapKey is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animationMapKey\",\n);\nexport const animations = registerClientReference(\n    function() { throw new Error(\"Attempted to call animations() from the server but animations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"animations\",\n);\nexport const anticipate = registerClientReference(\n    function() { throw new Error(\"Attempted to call anticipate() from the server but anticipate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"anticipate\",\n);\nexport const applyGeneratorOptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call applyGeneratorOptions() from the server but applyGeneratorOptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"applyGeneratorOptions\",\n);\nexport const applyPxDefaults = registerClientReference(\n    function() { throw new Error(\"Attempted to call applyPxDefaults() from the server but applyPxDefaults is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"applyPxDefaults\",\n);\nexport const attachSpring = registerClientReference(\n    function() { throw new Error(\"Attempted to call attachSpring() from the server but attachSpring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"attachSpring\",\n);\nexport const attrEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call attrEffect() from the server but attrEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"attrEffect\",\n);\nexport const backIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call backIn() from the server but backIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"backIn\",\n);\nexport const backInOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call backInOut() from the server but backInOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"backInOut\",\n);\nexport const backOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call backOut() from the server but backOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"backOut\",\n);\nexport const buildTransform = registerClientReference(\n    function() { throw new Error(\"Attempted to call buildTransform() from the server but buildTransform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"buildTransform\",\n);\nexport const calcGeneratorDuration = registerClientReference(\n    function() { throw new Error(\"Attempted to call calcGeneratorDuration() from the server but calcGeneratorDuration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"calcGeneratorDuration\",\n);\nexport const calcLength = registerClientReference(\n    function() { throw new Error(\"Attempted to call calcLength() from the server but calcLength is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"calcLength\",\n);\nexport const cancelFrame = registerClientReference(\n    function() { throw new Error(\"Attempted to call cancelFrame() from the server but cancelFrame is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"cancelFrame\",\n);\nexport const cancelMicrotask = registerClientReference(\n    function() { throw new Error(\"Attempted to call cancelMicrotask() from the server but cancelMicrotask is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"cancelMicrotask\",\n);\nexport const cancelSync = registerClientReference(\n    function() { throw new Error(\"Attempted to call cancelSync() from the server but cancelSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"cancelSync\",\n);\nexport const circIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call circIn() from the server but circIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"circIn\",\n);\nexport const circInOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call circInOut() from the server but circInOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"circInOut\",\n);\nexport const circOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call circOut() from the server but circOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"circOut\",\n);\nexport const clamp = registerClientReference(\n    function() { throw new Error(\"Attempted to call clamp() from the server but clamp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"clamp\",\n);\nexport const collectMotionValues = registerClientReference(\n    function() { throw new Error(\"Attempted to call collectMotionValues() from the server but collectMotionValues is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"collectMotionValues\",\n);\nexport const color = registerClientReference(\n    function() { throw new Error(\"Attempted to call color() from the server but color is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"color\",\n);\nexport const complex = registerClientReference(\n    function() { throw new Error(\"Attempted to call complex() from the server but complex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"complex\",\n);\nexport const convertOffsetToTimes = registerClientReference(\n    function() { throw new Error(\"Attempted to call convertOffsetToTimes() from the server but convertOffsetToTimes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"convertOffsetToTimes\",\n);\nexport const createBox = registerClientReference(\n    function() { throw new Error(\"Attempted to call createBox() from the server but createBox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"createBox\",\n);\nexport const createGeneratorEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call createGeneratorEasing() from the server but createGeneratorEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"createGeneratorEasing\",\n);\nexport const createRenderBatcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call createRenderBatcher() from the server but createRenderBatcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"createRenderBatcher\",\n);\nexport const createScopedAnimate = registerClientReference(\n    function() { throw new Error(\"Attempted to call createScopedAnimate() from the server but createScopedAnimate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"createScopedAnimate\",\n);\nexport const cubicBezier = registerClientReference(\n    function() { throw new Error(\"Attempted to call cubicBezier() from the server but cubicBezier is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"cubicBezier\",\n);\nexport const cubicBezierAsString = registerClientReference(\n    function() { throw new Error(\"Attempted to call cubicBezierAsString() from the server but cubicBezierAsString is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"cubicBezierAsString\",\n);\nexport const defaultEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultEasing() from the server but defaultEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"defaultEasing\",\n);\nexport const defaultOffset = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultOffset() from the server but defaultOffset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"defaultOffset\",\n);\nexport const defaultTransformValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultTransformValue() from the server but defaultTransformValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"defaultTransformValue\",\n);\nexport const defaultValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultValueTypes() from the server but defaultValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"defaultValueTypes\",\n);\nexport const degrees = registerClientReference(\n    function() { throw new Error(\"Attempted to call degrees() from the server but degrees is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"degrees\",\n);\nexport const delay = registerClientReference(\n    function() { throw new Error(\"Attempted to call delay() from the server but delay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"delay\",\n);\nexport const dimensionValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call dimensionValueTypes() from the server but dimensionValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"dimensionValueTypes\",\n);\nexport const disableInstantTransitions = registerClientReference(\n    function() { throw new Error(\"Attempted to call disableInstantTransitions() from the server but disableInstantTransitions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"disableInstantTransitions\",\n);\nexport const distance = registerClientReference(\n    function() { throw new Error(\"Attempted to call distance() from the server but distance is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"distance\",\n);\nexport const distance2D = registerClientReference(\n    function() { throw new Error(\"Attempted to call distance2D() from the server but distance2D is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"distance2D\",\n);\nexport const domAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call domAnimation() from the server but domAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"domAnimation\",\n);\nexport const domMax = registerClientReference(\n    function() { throw new Error(\"Attempted to call domMax() from the server but domMax is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"domMax\",\n);\nexport const domMin = registerClientReference(\n    function() { throw new Error(\"Attempted to call domMin() from the server but domMin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"domMin\",\n);\nexport const easeIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call easeIn() from the server but easeIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"easeIn\",\n);\nexport const easeInOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call easeInOut() from the server but easeInOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"easeInOut\",\n);\nexport const easeOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call easeOut() from the server but easeOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"easeOut\",\n);\nexport const easingDefinitionToFunction = registerClientReference(\n    function() { throw new Error(\"Attempted to call easingDefinitionToFunction() from the server but easingDefinitionToFunction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"easingDefinitionToFunction\",\n);\nexport const fillOffset = registerClientReference(\n    function() { throw new Error(\"Attempted to call fillOffset() from the server but fillOffset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"fillOffset\",\n);\nexport const fillWildcards = registerClientReference(\n    function() { throw new Error(\"Attempted to call fillWildcards() from the server but fillWildcards is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"fillWildcards\",\n);\nexport const filterProps = registerClientReference(\n    function() { throw new Error(\"Attempted to call filterProps() from the server but filterProps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"filterProps\",\n);\nexport const findDimensionValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call findDimensionValueType() from the server but findDimensionValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"findDimensionValueType\",\n);\nexport const findValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call findValueType() from the server but findValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"findValueType\",\n);\nexport const flushKeyframeResolvers = registerClientReference(\n    function() { throw new Error(\"Attempted to call flushKeyframeResolvers() from the server but flushKeyframeResolvers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"flushKeyframeResolvers\",\n);\nexport const frame = registerClientReference(\n    function() { throw new Error(\"Attempted to call frame() from the server but frame is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"frame\",\n);\nexport const frameData = registerClientReference(\n    function() { throw new Error(\"Attempted to call frameData() from the server but frameData is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"frameData\",\n);\nexport const frameSteps = registerClientReference(\n    function() { throw new Error(\"Attempted to call frameSteps() from the server but frameSteps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"frameSteps\",\n);\nexport const generateLinearEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call generateLinearEasing() from the server but generateLinearEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"generateLinearEasing\",\n);\nexport const getAnimatableNone = registerClientReference(\n    function() { throw new Error(\"Attempted to call getAnimatableNone() from the server but getAnimatableNone is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getAnimatableNone\",\n);\nexport const getAnimationMap = registerClientReference(\n    function() { throw new Error(\"Attempted to call getAnimationMap() from the server but getAnimationMap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getAnimationMap\",\n);\nexport const getComputedStyle = registerClientReference(\n    function() { throw new Error(\"Attempted to call getComputedStyle() from the server but getComputedStyle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getComputedStyle\",\n);\nexport const getDefaultValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call getDefaultValueType() from the server but getDefaultValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getDefaultValueType\",\n);\nexport const getEasingForSegment = registerClientReference(\n    function() { throw new Error(\"Attempted to call getEasingForSegment() from the server but getEasingForSegment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getEasingForSegment\",\n);\nexport const getMixer = registerClientReference(\n    function() { throw new Error(\"Attempted to call getMixer() from the server but getMixer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getMixer\",\n);\nexport const getOriginIndex = registerClientReference(\n    function() { throw new Error(\"Attempted to call getOriginIndex() from the server but getOriginIndex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getOriginIndex\",\n);\nexport const getValueAsType = registerClientReference(\n    function() { throw new Error(\"Attempted to call getValueAsType() from the server but getValueAsType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getValueAsType\",\n);\nexport const getValueTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call getValueTransition() from the server but getValueTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getValueTransition\",\n);\nexport const getVariableValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call getVariableValue() from the server but getVariableValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getVariableValue\",\n);\nexport const getViewAnimationLayerInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call getViewAnimationLayerInfo() from the server but getViewAnimationLayerInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getViewAnimationLayerInfo\",\n);\nexport const getViewAnimations = registerClientReference(\n    function() { throw new Error(\"Attempted to call getViewAnimations() from the server but getViewAnimations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"getViewAnimations\",\n);\nexport const hasWarned = registerClientReference(\n    function() { throw new Error(\"Attempted to call hasWarned() from the server but hasWarned is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"hasWarned\",\n);\nexport const hex = registerClientReference(\n    function() { throw new Error(\"Attempted to call hex() from the server but hex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"hex\",\n);\nexport const hover = registerClientReference(\n    function() { throw new Error(\"Attempted to call hover() from the server but hover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"hover\",\n);\nexport const hsla = registerClientReference(\n    function() { throw new Error(\"Attempted to call hsla() from the server but hsla is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"hsla\",\n);\nexport const hslaToRgba = registerClientReference(\n    function() { throw new Error(\"Attempted to call hslaToRgba() from the server but hslaToRgba is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"hslaToRgba\",\n);\nexport const inView = registerClientReference(\n    function() { throw new Error(\"Attempted to call inView() from the server but inView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"inView\",\n);\nexport const inertia = registerClientReference(\n    function() { throw new Error(\"Attempted to call inertia() from the server but inertia is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"inertia\",\n);\nexport const interpolate = registerClientReference(\n    function() { throw new Error(\"Attempted to call interpolate() from the server but interpolate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"interpolate\",\n);\nexport const invariant = registerClientReference(\n    function() { throw new Error(\"Attempted to call invariant() from the server but invariant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"invariant\",\n);\nexport const invisibleValues = registerClientReference(\n    function() { throw new Error(\"Attempted to call invisibleValues() from the server but invisibleValues is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"invisibleValues\",\n);\nexport const isBezierDefinition = registerClientReference(\n    function() { throw new Error(\"Attempted to call isBezierDefinition() from the server but isBezierDefinition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isBezierDefinition\",\n);\nexport const isBrowser = registerClientReference(\n    function() { throw new Error(\"Attempted to call isBrowser() from the server but isBrowser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isBrowser\",\n);\nexport const isCSSVariableName = registerClientReference(\n    function() { throw new Error(\"Attempted to call isCSSVariableName() from the server but isCSSVariableName is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isCSSVariableName\",\n);\nexport const isCSSVariableToken = registerClientReference(\n    function() { throw new Error(\"Attempted to call isCSSVariableToken() from the server but isCSSVariableToken is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isCSSVariableToken\",\n);\nexport const isDragActive = registerClientReference(\n    function() { throw new Error(\"Attempted to call isDragActive() from the server but isDragActive is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isDragActive\",\n);\nexport const isDragging = registerClientReference(\n    function() { throw new Error(\"Attempted to call isDragging() from the server but isDragging is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isDragging\",\n);\nexport const isEasingArray = registerClientReference(\n    function() { throw new Error(\"Attempted to call isEasingArray() from the server but isEasingArray is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isEasingArray\",\n);\nexport const isGenerator = registerClientReference(\n    function() { throw new Error(\"Attempted to call isGenerator() from the server but isGenerator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isGenerator\",\n);\nexport const isHTMLElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call isHTMLElement() from the server but isHTMLElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isHTMLElement\",\n);\nexport const isMotionComponent = registerClientReference(\n    function() { throw new Error(\"Attempted to call isMotionComponent() from the server but isMotionComponent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isMotionComponent\",\n);\nexport const isMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call isMotionValue() from the server but isMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isMotionValue\",\n);\nexport const isNodeOrChild = registerClientReference(\n    function() { throw new Error(\"Attempted to call isNodeOrChild() from the server but isNodeOrChild is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isNodeOrChild\",\n);\nexport const isNumericalString = registerClientReference(\n    function() { throw new Error(\"Attempted to call isNumericalString() from the server but isNumericalString is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isNumericalString\",\n);\nexport const isObject = registerClientReference(\n    function() { throw new Error(\"Attempted to call isObject() from the server but isObject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isObject\",\n);\nexport const isPrimaryPointer = registerClientReference(\n    function() { throw new Error(\"Attempted to call isPrimaryPointer() from the server but isPrimaryPointer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isPrimaryPointer\",\n);\nexport const isSVGElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call isSVGElement() from the server but isSVGElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isSVGElement\",\n);\nexport const isSVGSVGElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call isSVGSVGElement() from the server but isSVGSVGElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isSVGSVGElement\",\n);\nexport const isValidMotionProp = registerClientReference(\n    function() { throw new Error(\"Attempted to call isValidMotionProp() from the server but isValidMotionProp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isValidMotionProp\",\n);\nexport const isWaapiSupportedEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call isWaapiSupportedEasing() from the server but isWaapiSupportedEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isWaapiSupportedEasing\",\n);\nexport const isZeroValueString = registerClientReference(\n    function() { throw new Error(\"Attempted to call isZeroValueString() from the server but isZeroValueString is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"isZeroValueString\",\n);\nexport const keyframes = registerClientReference(\n    function() { throw new Error(\"Attempted to call keyframes() from the server but keyframes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"keyframes\",\n);\nexport const m = registerClientReference(\n    function() { throw new Error(\"Attempted to call m() from the server but m is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"m\",\n);\nexport const makeAnimationInstant = registerClientReference(\n    function() { throw new Error(\"Attempted to call makeAnimationInstant() from the server but makeAnimationInstant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"makeAnimationInstant\",\n);\nexport const makeUseVisualState = registerClientReference(\n    function() { throw new Error(\"Attempted to call makeUseVisualState() from the server but makeUseVisualState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"makeUseVisualState\",\n);\nexport const mapEasingToNativeEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call mapEasingToNativeEasing() from the server but mapEasingToNativeEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mapEasingToNativeEasing\",\n);\nexport const mapValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call mapValue() from the server but mapValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mapValue\",\n);\nexport const maxGeneratorDuration = registerClientReference(\n    function() { throw new Error(\"Attempted to call maxGeneratorDuration() from the server but maxGeneratorDuration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"maxGeneratorDuration\",\n);\nexport const memo = registerClientReference(\n    function() { throw new Error(\"Attempted to call memo() from the server but memo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"memo\",\n);\nexport const microtask = registerClientReference(\n    function() { throw new Error(\"Attempted to call microtask() from the server but microtask is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"microtask\",\n);\nexport const millisecondsToSeconds = registerClientReference(\n    function() { throw new Error(\"Attempted to call millisecondsToSeconds() from the server but millisecondsToSeconds is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"millisecondsToSeconds\",\n);\nexport const mirrorEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call mirrorEasing() from the server but mirrorEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mirrorEasing\",\n);\nexport const mix = registerClientReference(\n    function() { throw new Error(\"Attempted to call mix() from the server but mix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mix\",\n);\nexport const mixArray = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixArray() from the server but mixArray is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixArray\",\n);\nexport const mixColor = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixColor() from the server but mixColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixColor\",\n);\nexport const mixComplex = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixComplex() from the server but mixComplex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixComplex\",\n);\nexport const mixImmediate = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixImmediate() from the server but mixImmediate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixImmediate\",\n);\nexport const mixLinearColor = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixLinearColor() from the server but mixLinearColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixLinearColor\",\n);\nexport const mixNumber = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixNumber() from the server but mixNumber is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixNumber\",\n);\nexport const mixObject = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixObject() from the server but mixObject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixObject\",\n);\nexport const mixVisibility = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixVisibility() from the server but mixVisibility is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"mixVisibility\",\n);\nexport const motion = registerClientReference(\n    function() { throw new Error(\"Attempted to call motion() from the server but motion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"motion\",\n);\nexport const motionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call motionValue() from the server but motionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"motionValue\",\n);\nexport const moveItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call moveItem() from the server but moveItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"moveItem\",\n);\nexport const noop = registerClientReference(\n    function() { throw new Error(\"Attempted to call noop() from the server but noop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"noop\",\n);\nexport const number = registerClientReference(\n    function() { throw new Error(\"Attempted to call number() from the server but number is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"number\",\n);\nexport const numberValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call numberValueTypes() from the server but numberValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"numberValueTypes\",\n);\nexport const observeTimeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call observeTimeline() from the server but observeTimeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"observeTimeline\",\n);\nexport const optimizedAppearDataAttribute = registerClientReference(\n    function() { throw new Error(\"Attempted to call optimizedAppearDataAttribute() from the server but optimizedAppearDataAttribute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"optimizedAppearDataAttribute\",\n);\nexport const parseCSSVariable = registerClientReference(\n    function() { throw new Error(\"Attempted to call parseCSSVariable() from the server but parseCSSVariable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"parseCSSVariable\",\n);\nexport const parseValueFromTransform = registerClientReference(\n    function() { throw new Error(\"Attempted to call parseValueFromTransform() from the server but parseValueFromTransform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"parseValueFromTransform\",\n);\nexport const percent = registerClientReference(\n    function() { throw new Error(\"Attempted to call percent() from the server but percent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"percent\",\n);\nexport const pipe = registerClientReference(\n    function() { throw new Error(\"Attempted to call pipe() from the server but pipe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"pipe\",\n);\nexport const positionalKeys = registerClientReference(\n    function() { throw new Error(\"Attempted to call positionalKeys() from the server but positionalKeys is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"positionalKeys\",\n);\nexport const press = registerClientReference(\n    function() { throw new Error(\"Attempted to call press() from the server but press is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"press\",\n);\nexport const progress = registerClientReference(\n    function() { throw new Error(\"Attempted to call progress() from the server but progress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"progress\",\n);\nexport const progressPercentage = registerClientReference(\n    function() { throw new Error(\"Attempted to call progressPercentage() from the server but progressPercentage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"progressPercentage\",\n);\nexport const propEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call propEffect() from the server but propEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"propEffect\",\n);\nexport const px = registerClientReference(\n    function() { throw new Error(\"Attempted to call px() from the server but px is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"px\",\n);\nexport const readTransformValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call readTransformValue() from the server but readTransformValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"readTransformValue\",\n);\nexport const recordStats = registerClientReference(\n    function() { throw new Error(\"Attempted to call recordStats() from the server but recordStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"recordStats\",\n);\nexport const removeItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call removeItem() from the server but removeItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"removeItem\",\n);\nexport const resize = registerClientReference(\n    function() { throw new Error(\"Attempted to call resize() from the server but resize is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"resize\",\n);\nexport const resolveElements = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveElements() from the server but resolveElements is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"resolveElements\",\n);\nexport const resolveMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveMotionValue() from the server but resolveMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"resolveMotionValue\",\n);\nexport const reverseEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call reverseEasing() from the server but reverseEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"reverseEasing\",\n);\nexport const rgbUnit = registerClientReference(\n    function() { throw new Error(\"Attempted to call rgbUnit() from the server but rgbUnit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"rgbUnit\",\n);\nexport const rgba = registerClientReference(\n    function() { throw new Error(\"Attempted to call rgba() from the server but rgba is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"rgba\",\n);\nexport const scale = registerClientReference(\n    function() { throw new Error(\"Attempted to call scale() from the server but scale is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"scale\",\n);\nexport const scroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call scroll() from the server but scroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"scroll\",\n);\nexport const scrollInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call scrollInfo() from the server but scrollInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"scrollInfo\",\n);\nexport const secondsToMilliseconds = registerClientReference(\n    function() { throw new Error(\"Attempted to call secondsToMilliseconds() from the server but secondsToMilliseconds is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"secondsToMilliseconds\",\n);\nexport const setDragLock = registerClientReference(\n    function() { throw new Error(\"Attempted to call setDragLock() from the server but setDragLock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"setDragLock\",\n);\nexport const setStyle = registerClientReference(\n    function() { throw new Error(\"Attempted to call setStyle() from the server but setStyle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"setStyle\",\n);\nexport const spring = registerClientReference(\n    function() { throw new Error(\"Attempted to call spring() from the server but spring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"spring\",\n);\nexport const springValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call springValue() from the server but springValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"springValue\",\n);\nexport const stagger = registerClientReference(\n    function() { throw new Error(\"Attempted to call stagger() from the server but stagger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"stagger\",\n);\nexport const startOptimizedAppearAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call startOptimizedAppearAnimation() from the server but startOptimizedAppearAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"startOptimizedAppearAnimation\",\n);\nexport const startWaapiAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call startWaapiAnimation() from the server but startWaapiAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"startWaapiAnimation\",\n);\nexport const statsBuffer = registerClientReference(\n    function() { throw new Error(\"Attempted to call statsBuffer() from the server but statsBuffer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"statsBuffer\",\n);\nexport const steps = registerClientReference(\n    function() { throw new Error(\"Attempted to call steps() from the server but steps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"steps\",\n);\nexport const styleEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call styleEffect() from the server but styleEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"styleEffect\",\n);\nexport const supportedWaapiEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportedWaapiEasing() from the server but supportedWaapiEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"supportedWaapiEasing\",\n);\nexport const supportsBrowserAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsBrowserAnimation() from the server but supportsBrowserAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"supportsBrowserAnimation\",\n);\nexport const supportsFlags = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsFlags() from the server but supportsFlags is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"supportsFlags\",\n);\nexport const supportsLinearEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsLinearEasing() from the server but supportsLinearEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"supportsLinearEasing\",\n);\nexport const supportsPartialKeyframes = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsPartialKeyframes() from the server but supportsPartialKeyframes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"supportsPartialKeyframes\",\n);\nexport const supportsScrollTimeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsScrollTimeline() from the server but supportsScrollTimeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"supportsScrollTimeline\",\n);\nexport const svgEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call svgEffect() from the server but svgEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"svgEffect\",\n);\nexport const sync = registerClientReference(\n    function() { throw new Error(\"Attempted to call sync() from the server but sync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"sync\",\n);\nexport const testValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call testValueType() from the server but testValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"testValueType\",\n);\nexport const time = registerClientReference(\n    function() { throw new Error(\"Attempted to call time() from the server but time is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"time\",\n);\nexport const transform = registerClientReference(\n    function() { throw new Error(\"Attempted to call transform() from the server but transform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"transform\",\n);\nexport const transformPropOrder = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformPropOrder() from the server but transformPropOrder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"transformPropOrder\",\n);\nexport const transformProps = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformProps() from the server but transformProps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"transformProps\",\n);\nexport const transformValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformValue() from the server but transformValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"transformValue\",\n);\nexport const transformValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformValueTypes() from the server but transformValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"transformValueTypes\",\n);\nexport const unwrapMotionComponent = registerClientReference(\n    function() { throw new Error(\"Attempted to call unwrapMotionComponent() from the server but unwrapMotionComponent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"unwrapMotionComponent\",\n);\nexport const useAnimate = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimate() from the server but useAnimate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useAnimate\",\n);\nexport const useAnimateMini = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimateMini() from the server but useAnimateMini is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useAnimateMini\",\n);\nexport const useAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimation() from the server but useAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useAnimation\",\n);\nexport const useAnimationControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimationControls() from the server but useAnimationControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useAnimationControls\",\n);\nexport const useAnimationFrame = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimationFrame() from the server but useAnimationFrame is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useAnimationFrame\",\n);\nexport const useCycle = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCycle() from the server but useCycle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useCycle\",\n);\nexport const useDeprecatedAnimatedState = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDeprecatedAnimatedState() from the server but useDeprecatedAnimatedState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useDeprecatedAnimatedState\",\n);\nexport const useDeprecatedInvertedScale = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDeprecatedInvertedScale() from the server but useDeprecatedInvertedScale is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useDeprecatedInvertedScale\",\n);\nexport const useDomEvent = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDomEvent() from the server but useDomEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useDomEvent\",\n);\nexport const useDragControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDragControls() from the server but useDragControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useDragControls\",\n);\nexport const useElementScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useElementScroll() from the server but useElementScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useElementScroll\",\n);\nexport const useForceUpdate = registerClientReference(\n    function() { throw new Error(\"Attempted to call useForceUpdate() from the server but useForceUpdate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useForceUpdate\",\n);\nexport const useInView = registerClientReference(\n    function() { throw new Error(\"Attempted to call useInView() from the server but useInView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useInView\",\n);\nexport const useInstantLayoutTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call useInstantLayoutTransition() from the server but useInstantLayoutTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useInstantLayoutTransition\",\n);\nexport const useInstantTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call useInstantTransition() from the server but useInstantTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useInstantTransition\",\n);\nexport const useIsPresent = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsPresent() from the server but useIsPresent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useIsPresent\",\n);\nexport const useIsomorphicLayoutEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsomorphicLayoutEffect() from the server but useIsomorphicLayoutEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useIsomorphicLayoutEffect\",\n);\nexport const useMotionTemplate = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMotionTemplate() from the server but useMotionTemplate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useMotionTemplate\",\n);\nexport const useMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMotionValue() from the server but useMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useMotionValue\",\n);\nexport const useMotionValueEvent = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMotionValueEvent() from the server but useMotionValueEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useMotionValueEvent\",\n);\nexport const usePageInView = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePageInView() from the server but usePageInView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"usePageInView\",\n);\nexport const usePresence = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePresence() from the server but usePresence is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"usePresence\",\n);\nexport const usePresenceData = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePresenceData() from the server but usePresenceData is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"usePresenceData\",\n);\nexport const useReducedMotion = registerClientReference(\n    function() { throw new Error(\"Attempted to call useReducedMotion() from the server but useReducedMotion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useReducedMotion\",\n);\nexport const useReducedMotionConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call useReducedMotionConfig() from the server but useReducedMotionConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useReducedMotionConfig\",\n);\nexport const useResetProjection = registerClientReference(\n    function() { throw new Error(\"Attempted to call useResetProjection() from the server but useResetProjection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useResetProjection\",\n);\nexport const useScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useScroll() from the server but useScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useScroll\",\n);\nexport const useSpring = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSpring() from the server but useSpring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useSpring\",\n);\nexport const useTime = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTime() from the server but useTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useTime\",\n);\nexport const useTransform = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTransform() from the server but useTransform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useTransform\",\n);\nexport const useUnmountEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUnmountEffect() from the server but useUnmountEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useUnmountEffect\",\n);\nexport const useVelocity = registerClientReference(\n    function() { throw new Error(\"Attempted to call useVelocity() from the server but useVelocity is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useVelocity\",\n);\nexport const useViewportScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useViewportScroll() from the server but useViewportScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useViewportScroll\",\n);\nexport const useWillChange = registerClientReference(\n    function() { throw new Error(\"Attempted to call useWillChange() from the server but useWillChange is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"useWillChange\",\n);\nexport const velocityPerSecond = registerClientReference(\n    function() { throw new Error(\"Attempted to call velocityPerSecond() from the server but velocityPerSecond is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"velocityPerSecond\",\n);\nexport const vh = registerClientReference(\n    function() { throw new Error(\"Attempted to call vh() from the server but vh is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"vh\",\n);\nexport const visualElementStore = registerClientReference(\n    function() { throw new Error(\"Attempted to call visualElementStore() from the server but visualElementStore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"visualElementStore\",\n);\nexport const vw = registerClientReference(\n    function() { throw new Error(\"Attempted to call vw() from the server but vw is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"vw\",\n);\nexport const warnOnce = registerClientReference(\n    function() { throw new Error(\"Attempted to call warnOnce() from the server but warnOnce is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"warnOnce\",\n);\nexport const warning = registerClientReference(\n    function() { throw new Error(\"Attempted to call warning() from the server but warning is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"warning\",\n);\nexport const wrap = registerClientReference(\n    function() { throw new Error(\"Attempted to call wrap() from the server but wrap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs <module evaluation>\",\n    \"wrap\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,+BAA+B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9D;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,8EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,IAAI,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnC;IAAa,MAAM,IAAI,MAAM;AAAkN,GAC/O,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,+BAA+B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9D;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,KAAK,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpC;IAAa,MAAM,IAAI,MAAM;AAAoN,GACjP,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,gCAAgC,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/D;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,8EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,8EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,8EACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,8EACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8EACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8EACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8EACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,8EACA;AAEG,MAAM,KAAK,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpC;IAAa,MAAM,IAAI,MAAM;AAAoN,GACjP,8EACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,8EACA;AAEG,MAAM,KAAK,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpC;IAAa,MAAM,IAAI,MAAM;AAAoN,GACjP,8EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8EACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/framer-motion/dist/es/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AnimatePresence = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimatePresence() from the server but AnimatePresence is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"AnimatePresence\",\n);\nexport const AnimateSharedLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AnimateSharedLayout() from the server but AnimateSharedLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"AnimateSharedLayout\",\n);\nexport const AsyncMotionValueAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call AsyncMotionV<PERSON>ueAnimation() from the server but AsyncMotionValueAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"AsyncMotionValueAnimation\",\n);\nexport const DOMKeyframesResolver = registerClientReference(\n    function() { throw new Error(\"Attempted to call DOMKeyframesResolver() from the server but DOMKeyframesResolver is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"DOMKeyframesResolver\",\n);\nexport const DeprecatedLayoutGroupContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call DeprecatedLayoutGroupContext() from the server but DeprecatedLayoutGroupContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"DeprecatedLayoutGroupContext\",\n);\nexport const DragControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call DragControls() from the server but DragControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"DragControls\",\n);\nexport const FlatTree = registerClientReference(\n    function() { throw new Error(\"Attempted to call FlatTree() from the server but FlatTree is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"FlatTree\",\n);\nexport const GroupAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call GroupAnimation() from the server but GroupAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"GroupAnimation\",\n);\nexport const GroupAnimationWithThen = registerClientReference(\n    function() { throw new Error(\"Attempted to call GroupAnimationWithThen() from the server but GroupAnimationWithThen is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"GroupAnimationWithThen\",\n);\nexport const JSAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call JSAnimation() from the server but JSAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"JSAnimation\",\n);\nexport const KeyframeResolver = registerClientReference(\n    function() { throw new Error(\"Attempted to call KeyframeResolver() from the server but KeyframeResolver is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"KeyframeResolver\",\n);\nexport const LayoutGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutGroup() from the server but LayoutGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"LayoutGroup\",\n);\nexport const LayoutGroupContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call LayoutGroupContext() from the server but LayoutGroupContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"LayoutGroupContext\",\n);\nexport const LazyMotion = registerClientReference(\n    function() { throw new Error(\"Attempted to call LazyMotion() from the server but LazyMotion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"LazyMotion\",\n);\nexport const MotionConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionConfig() from the server but MotionConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"MotionConfig\",\n);\nexport const MotionConfigContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionConfigContext() from the server but MotionConfigContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"MotionConfigContext\",\n);\nexport const MotionContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionContext() from the server but MotionContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"MotionContext\",\n);\nexport const MotionGlobalConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionGlobalConfig() from the server but MotionGlobalConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"MotionGlobalConfig\",\n);\nexport const MotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call MotionValue() from the server but MotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"MotionValue\",\n);\nexport const NativeAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call NativeAnimation() from the server but NativeAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"NativeAnimation\",\n);\nexport const NativeAnimationExtended = registerClientReference(\n    function() { throw new Error(\"Attempted to call NativeAnimationExtended() from the server but NativeAnimationExtended is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"NativeAnimationExtended\",\n);\nexport const NativeAnimationWrapper = registerClientReference(\n    function() { throw new Error(\"Attempted to call NativeAnimationWrapper() from the server but NativeAnimationWrapper is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"NativeAnimationWrapper\",\n);\nexport const PresenceContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call PresenceContext() from the server but PresenceContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"PresenceContext\",\n);\nexport const Reorder = registerClientReference(\n    function() { throw new Error(\"Attempted to call Reorder() from the server but Reorder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"Reorder\",\n);\nexport const SubscriptionManager = registerClientReference(\n    function() { throw new Error(\"Attempted to call SubscriptionManager() from the server but SubscriptionManager is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"SubscriptionManager\",\n);\nexport const SwitchLayoutGroupContext = registerClientReference(\n    function() { throw new Error(\"Attempted to call SwitchLayoutGroupContext() from the server but SwitchLayoutGroupContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"SwitchLayoutGroupContext\",\n);\nexport const ViewTransitionBuilder = registerClientReference(\n    function() { throw new Error(\"Attempted to call ViewTransitionBuilder() from the server but ViewTransitionBuilder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"ViewTransitionBuilder\",\n);\nexport const VisualElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call VisualElement() from the server but VisualElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"VisualElement\",\n);\nexport const WillChangeMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call WillChangeMotionValue() from the server but WillChangeMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"WillChangeMotionValue\",\n);\nexport const acceleratedValues = registerClientReference(\n    function() { throw new Error(\"Attempted to call acceleratedValues() from the server but acceleratedValues is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"acceleratedValues\",\n);\nexport const activeAnimations = registerClientReference(\n    function() { throw new Error(\"Attempted to call activeAnimations() from the server but activeAnimations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"activeAnimations\",\n);\nexport const addAttrValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call addAttrValue() from the server but addAttrValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"addAttrValue\",\n);\nexport const addPointerEvent = registerClientReference(\n    function() { throw new Error(\"Attempted to call addPointerEvent() from the server but addPointerEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"addPointerEvent\",\n);\nexport const addPointerInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call addPointerInfo() from the server but addPointerInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"addPointerInfo\",\n);\nexport const addScaleCorrector = registerClientReference(\n    function() { throw new Error(\"Attempted to call addScaleCorrector() from the server but addScaleCorrector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"addScaleCorrector\",\n);\nexport const addStyleValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call addStyleValue() from the server but addStyleValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"addStyleValue\",\n);\nexport const addUniqueItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call addUniqueItem() from the server but addUniqueItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"addUniqueItem\",\n);\nexport const alpha = registerClientReference(\n    function() { throw new Error(\"Attempted to call alpha() from the server but alpha is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"alpha\",\n);\nexport const analyseComplexValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call analyseComplexValue() from the server but analyseComplexValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"analyseComplexValue\",\n);\nexport const animate = registerClientReference(\n    function() { throw new Error(\"Attempted to call animate() from the server but animate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animate\",\n);\nexport const animateMini = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateMini() from the server but animateMini is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animateMini\",\n);\nexport const animateValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateValue() from the server but animateValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animateValue\",\n);\nexport const animateView = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateView() from the server but animateView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animateView\",\n);\nexport const animateVisualElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call animateVisualElement() from the server but animateVisualElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animateVisualElement\",\n);\nexport const animationControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call animationControls() from the server but animationControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animationControls\",\n);\nexport const animationMapKey = registerClientReference(\n    function() { throw new Error(\"Attempted to call animationMapKey() from the server but animationMapKey is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animationMapKey\",\n);\nexport const animations = registerClientReference(\n    function() { throw new Error(\"Attempted to call animations() from the server but animations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"animations\",\n);\nexport const anticipate = registerClientReference(\n    function() { throw new Error(\"Attempted to call anticipate() from the server but anticipate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"anticipate\",\n);\nexport const applyGeneratorOptions = registerClientReference(\n    function() { throw new Error(\"Attempted to call applyGeneratorOptions() from the server but applyGeneratorOptions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"applyGeneratorOptions\",\n);\nexport const applyPxDefaults = registerClientReference(\n    function() { throw new Error(\"Attempted to call applyPxDefaults() from the server but applyPxDefaults is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"applyPxDefaults\",\n);\nexport const attachSpring = registerClientReference(\n    function() { throw new Error(\"Attempted to call attachSpring() from the server but attachSpring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"attachSpring\",\n);\nexport const attrEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call attrEffect() from the server but attrEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"attrEffect\",\n);\nexport const backIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call backIn() from the server but backIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"backIn\",\n);\nexport const backInOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call backInOut() from the server but backInOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"backInOut\",\n);\nexport const backOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call backOut() from the server but backOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"backOut\",\n);\nexport const buildTransform = registerClientReference(\n    function() { throw new Error(\"Attempted to call buildTransform() from the server but buildTransform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"buildTransform\",\n);\nexport const calcGeneratorDuration = registerClientReference(\n    function() { throw new Error(\"Attempted to call calcGeneratorDuration() from the server but calcGeneratorDuration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"calcGeneratorDuration\",\n);\nexport const calcLength = registerClientReference(\n    function() { throw new Error(\"Attempted to call calcLength() from the server but calcLength is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"calcLength\",\n);\nexport const cancelFrame = registerClientReference(\n    function() { throw new Error(\"Attempted to call cancelFrame() from the server but cancelFrame is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"cancelFrame\",\n);\nexport const cancelMicrotask = registerClientReference(\n    function() { throw new Error(\"Attempted to call cancelMicrotask() from the server but cancelMicrotask is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"cancelMicrotask\",\n);\nexport const cancelSync = registerClientReference(\n    function() { throw new Error(\"Attempted to call cancelSync() from the server but cancelSync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"cancelSync\",\n);\nexport const circIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call circIn() from the server but circIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"circIn\",\n);\nexport const circInOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call circInOut() from the server but circInOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"circInOut\",\n);\nexport const circOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call circOut() from the server but circOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"circOut\",\n);\nexport const clamp = registerClientReference(\n    function() { throw new Error(\"Attempted to call clamp() from the server but clamp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"clamp\",\n);\nexport const collectMotionValues = registerClientReference(\n    function() { throw new Error(\"Attempted to call collectMotionValues() from the server but collectMotionValues is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"collectMotionValues\",\n);\nexport const color = registerClientReference(\n    function() { throw new Error(\"Attempted to call color() from the server but color is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"color\",\n);\nexport const complex = registerClientReference(\n    function() { throw new Error(\"Attempted to call complex() from the server but complex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"complex\",\n);\nexport const convertOffsetToTimes = registerClientReference(\n    function() { throw new Error(\"Attempted to call convertOffsetToTimes() from the server but convertOffsetToTimes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"convertOffsetToTimes\",\n);\nexport const createBox = registerClientReference(\n    function() { throw new Error(\"Attempted to call createBox() from the server but createBox is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"createBox\",\n);\nexport const createGeneratorEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call createGeneratorEasing() from the server but createGeneratorEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"createGeneratorEasing\",\n);\nexport const createRenderBatcher = registerClientReference(\n    function() { throw new Error(\"Attempted to call createRenderBatcher() from the server but createRenderBatcher is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"createRenderBatcher\",\n);\nexport const createScopedAnimate = registerClientReference(\n    function() { throw new Error(\"Attempted to call createScopedAnimate() from the server but createScopedAnimate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"createScopedAnimate\",\n);\nexport const cubicBezier = registerClientReference(\n    function() { throw new Error(\"Attempted to call cubicBezier() from the server but cubicBezier is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"cubicBezier\",\n);\nexport const cubicBezierAsString = registerClientReference(\n    function() { throw new Error(\"Attempted to call cubicBezierAsString() from the server but cubicBezierAsString is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"cubicBezierAsString\",\n);\nexport const defaultEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultEasing() from the server but defaultEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"defaultEasing\",\n);\nexport const defaultOffset = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultOffset() from the server but defaultOffset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"defaultOffset\",\n);\nexport const defaultTransformValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultTransformValue() from the server but defaultTransformValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"defaultTransformValue\",\n);\nexport const defaultValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call defaultValueTypes() from the server but defaultValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"defaultValueTypes\",\n);\nexport const degrees = registerClientReference(\n    function() { throw new Error(\"Attempted to call degrees() from the server but degrees is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"degrees\",\n);\nexport const delay = registerClientReference(\n    function() { throw new Error(\"Attempted to call delay() from the server but delay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"delay\",\n);\nexport const dimensionValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call dimensionValueTypes() from the server but dimensionValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"dimensionValueTypes\",\n);\nexport const disableInstantTransitions = registerClientReference(\n    function() { throw new Error(\"Attempted to call disableInstantTransitions() from the server but disableInstantTransitions is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"disableInstantTransitions\",\n);\nexport const distance = registerClientReference(\n    function() { throw new Error(\"Attempted to call distance() from the server but distance is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"distance\",\n);\nexport const distance2D = registerClientReference(\n    function() { throw new Error(\"Attempted to call distance2D() from the server but distance2D is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"distance2D\",\n);\nexport const domAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call domAnimation() from the server but domAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"domAnimation\",\n);\nexport const domMax = registerClientReference(\n    function() { throw new Error(\"Attempted to call domMax() from the server but domMax is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"domMax\",\n);\nexport const domMin = registerClientReference(\n    function() { throw new Error(\"Attempted to call domMin() from the server but domMin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"domMin\",\n);\nexport const easeIn = registerClientReference(\n    function() { throw new Error(\"Attempted to call easeIn() from the server but easeIn is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"easeIn\",\n);\nexport const easeInOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call easeInOut() from the server but easeInOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"easeInOut\",\n);\nexport const easeOut = registerClientReference(\n    function() { throw new Error(\"Attempted to call easeOut() from the server but easeOut is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"easeOut\",\n);\nexport const easingDefinitionToFunction = registerClientReference(\n    function() { throw new Error(\"Attempted to call easingDefinitionToFunction() from the server but easingDefinitionToFunction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"easingDefinitionToFunction\",\n);\nexport const fillOffset = registerClientReference(\n    function() { throw new Error(\"Attempted to call fillOffset() from the server but fillOffset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"fillOffset\",\n);\nexport const fillWildcards = registerClientReference(\n    function() { throw new Error(\"Attempted to call fillWildcards() from the server but fillWildcards is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"fillWildcards\",\n);\nexport const filterProps = registerClientReference(\n    function() { throw new Error(\"Attempted to call filterProps() from the server but filterProps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"filterProps\",\n);\nexport const findDimensionValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call findDimensionValueType() from the server but findDimensionValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"findDimensionValueType\",\n);\nexport const findValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call findValueType() from the server but findValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"findValueType\",\n);\nexport const flushKeyframeResolvers = registerClientReference(\n    function() { throw new Error(\"Attempted to call flushKeyframeResolvers() from the server but flushKeyframeResolvers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"flushKeyframeResolvers\",\n);\nexport const frame = registerClientReference(\n    function() { throw new Error(\"Attempted to call frame() from the server but frame is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"frame\",\n);\nexport const frameData = registerClientReference(\n    function() { throw new Error(\"Attempted to call frameData() from the server but frameData is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"frameData\",\n);\nexport const frameSteps = registerClientReference(\n    function() { throw new Error(\"Attempted to call frameSteps() from the server but frameSteps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"frameSteps\",\n);\nexport const generateLinearEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call generateLinearEasing() from the server but generateLinearEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"generateLinearEasing\",\n);\nexport const getAnimatableNone = registerClientReference(\n    function() { throw new Error(\"Attempted to call getAnimatableNone() from the server but getAnimatableNone is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getAnimatableNone\",\n);\nexport const getAnimationMap = registerClientReference(\n    function() { throw new Error(\"Attempted to call getAnimationMap() from the server but getAnimationMap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getAnimationMap\",\n);\nexport const getComputedStyle = registerClientReference(\n    function() { throw new Error(\"Attempted to call getComputedStyle() from the server but getComputedStyle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getComputedStyle\",\n);\nexport const getDefaultValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call getDefaultValueType() from the server but getDefaultValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getDefaultValueType\",\n);\nexport const getEasingForSegment = registerClientReference(\n    function() { throw new Error(\"Attempted to call getEasingForSegment() from the server but getEasingForSegment is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getEasingForSegment\",\n);\nexport const getMixer = registerClientReference(\n    function() { throw new Error(\"Attempted to call getMixer() from the server but getMixer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getMixer\",\n);\nexport const getOriginIndex = registerClientReference(\n    function() { throw new Error(\"Attempted to call getOriginIndex() from the server but getOriginIndex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getOriginIndex\",\n);\nexport const getValueAsType = registerClientReference(\n    function() { throw new Error(\"Attempted to call getValueAsType() from the server but getValueAsType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getValueAsType\",\n);\nexport const getValueTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call getValueTransition() from the server but getValueTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getValueTransition\",\n);\nexport const getVariableValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call getVariableValue() from the server but getVariableValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getVariableValue\",\n);\nexport const getViewAnimationLayerInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call getViewAnimationLayerInfo() from the server but getViewAnimationLayerInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getViewAnimationLayerInfo\",\n);\nexport const getViewAnimations = registerClientReference(\n    function() { throw new Error(\"Attempted to call getViewAnimations() from the server but getViewAnimations is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"getViewAnimations\",\n);\nexport const hasWarned = registerClientReference(\n    function() { throw new Error(\"Attempted to call hasWarned() from the server but hasWarned is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"hasWarned\",\n);\nexport const hex = registerClientReference(\n    function() { throw new Error(\"Attempted to call hex() from the server but hex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"hex\",\n);\nexport const hover = registerClientReference(\n    function() { throw new Error(\"Attempted to call hover() from the server but hover is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"hover\",\n);\nexport const hsla = registerClientReference(\n    function() { throw new Error(\"Attempted to call hsla() from the server but hsla is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"hsla\",\n);\nexport const hslaToRgba = registerClientReference(\n    function() { throw new Error(\"Attempted to call hslaToRgba() from the server but hslaToRgba is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"hslaToRgba\",\n);\nexport const inView = registerClientReference(\n    function() { throw new Error(\"Attempted to call inView() from the server but inView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"inView\",\n);\nexport const inertia = registerClientReference(\n    function() { throw new Error(\"Attempted to call inertia() from the server but inertia is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"inertia\",\n);\nexport const interpolate = registerClientReference(\n    function() { throw new Error(\"Attempted to call interpolate() from the server but interpolate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"interpolate\",\n);\nexport const invariant = registerClientReference(\n    function() { throw new Error(\"Attempted to call invariant() from the server but invariant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"invariant\",\n);\nexport const invisibleValues = registerClientReference(\n    function() { throw new Error(\"Attempted to call invisibleValues() from the server but invisibleValues is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"invisibleValues\",\n);\nexport const isBezierDefinition = registerClientReference(\n    function() { throw new Error(\"Attempted to call isBezierDefinition() from the server but isBezierDefinition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isBezierDefinition\",\n);\nexport const isBrowser = registerClientReference(\n    function() { throw new Error(\"Attempted to call isBrowser() from the server but isBrowser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isBrowser\",\n);\nexport const isCSSVariableName = registerClientReference(\n    function() { throw new Error(\"Attempted to call isCSSVariableName() from the server but isCSSVariableName is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isCSSVariableName\",\n);\nexport const isCSSVariableToken = registerClientReference(\n    function() { throw new Error(\"Attempted to call isCSSVariableToken() from the server but isCSSVariableToken is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isCSSVariableToken\",\n);\nexport const isDragActive = registerClientReference(\n    function() { throw new Error(\"Attempted to call isDragActive() from the server but isDragActive is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isDragActive\",\n);\nexport const isDragging = registerClientReference(\n    function() { throw new Error(\"Attempted to call isDragging() from the server but isDragging is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isDragging\",\n);\nexport const isEasingArray = registerClientReference(\n    function() { throw new Error(\"Attempted to call isEasingArray() from the server but isEasingArray is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isEasingArray\",\n);\nexport const isGenerator = registerClientReference(\n    function() { throw new Error(\"Attempted to call isGenerator() from the server but isGenerator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isGenerator\",\n);\nexport const isHTMLElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call isHTMLElement() from the server but isHTMLElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isHTMLElement\",\n);\nexport const isMotionComponent = registerClientReference(\n    function() { throw new Error(\"Attempted to call isMotionComponent() from the server but isMotionComponent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isMotionComponent\",\n);\nexport const isMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call isMotionValue() from the server but isMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isMotionValue\",\n);\nexport const isNodeOrChild = registerClientReference(\n    function() { throw new Error(\"Attempted to call isNodeOrChild() from the server but isNodeOrChild is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isNodeOrChild\",\n);\nexport const isNumericalString = registerClientReference(\n    function() { throw new Error(\"Attempted to call isNumericalString() from the server but isNumericalString is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isNumericalString\",\n);\nexport const isObject = registerClientReference(\n    function() { throw new Error(\"Attempted to call isObject() from the server but isObject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isObject\",\n);\nexport const isPrimaryPointer = registerClientReference(\n    function() { throw new Error(\"Attempted to call isPrimaryPointer() from the server but isPrimaryPointer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isPrimaryPointer\",\n);\nexport const isSVGElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call isSVGElement() from the server but isSVGElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isSVGElement\",\n);\nexport const isSVGSVGElement = registerClientReference(\n    function() { throw new Error(\"Attempted to call isSVGSVGElement() from the server but isSVGSVGElement is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isSVGSVGElement\",\n);\nexport const isValidMotionProp = registerClientReference(\n    function() { throw new Error(\"Attempted to call isValidMotionProp() from the server but isValidMotionProp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isValidMotionProp\",\n);\nexport const isWaapiSupportedEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call isWaapiSupportedEasing() from the server but isWaapiSupportedEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isWaapiSupportedEasing\",\n);\nexport const isZeroValueString = registerClientReference(\n    function() { throw new Error(\"Attempted to call isZeroValueString() from the server but isZeroValueString is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"isZeroValueString\",\n);\nexport const keyframes = registerClientReference(\n    function() { throw new Error(\"Attempted to call keyframes() from the server but keyframes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"keyframes\",\n);\nexport const m = registerClientReference(\n    function() { throw new Error(\"Attempted to call m() from the server but m is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"m\",\n);\nexport const makeAnimationInstant = registerClientReference(\n    function() { throw new Error(\"Attempted to call makeAnimationInstant() from the server but makeAnimationInstant is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"makeAnimationInstant\",\n);\nexport const makeUseVisualState = registerClientReference(\n    function() { throw new Error(\"Attempted to call makeUseVisualState() from the server but makeUseVisualState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"makeUseVisualState\",\n);\nexport const mapEasingToNativeEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call mapEasingToNativeEasing() from the server but mapEasingToNativeEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mapEasingToNativeEasing\",\n);\nexport const mapValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call mapValue() from the server but mapValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mapValue\",\n);\nexport const maxGeneratorDuration = registerClientReference(\n    function() { throw new Error(\"Attempted to call maxGeneratorDuration() from the server but maxGeneratorDuration is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"maxGeneratorDuration\",\n);\nexport const memo = registerClientReference(\n    function() { throw new Error(\"Attempted to call memo() from the server but memo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"memo\",\n);\nexport const microtask = registerClientReference(\n    function() { throw new Error(\"Attempted to call microtask() from the server but microtask is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"microtask\",\n);\nexport const millisecondsToSeconds = registerClientReference(\n    function() { throw new Error(\"Attempted to call millisecondsToSeconds() from the server but millisecondsToSeconds is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"millisecondsToSeconds\",\n);\nexport const mirrorEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call mirrorEasing() from the server but mirrorEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mirrorEasing\",\n);\nexport const mix = registerClientReference(\n    function() { throw new Error(\"Attempted to call mix() from the server but mix is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mix\",\n);\nexport const mixArray = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixArray() from the server but mixArray is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixArray\",\n);\nexport const mixColor = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixColor() from the server but mixColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixColor\",\n);\nexport const mixComplex = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixComplex() from the server but mixComplex is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixComplex\",\n);\nexport const mixImmediate = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixImmediate() from the server but mixImmediate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixImmediate\",\n);\nexport const mixLinearColor = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixLinearColor() from the server but mixLinearColor is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixLinearColor\",\n);\nexport const mixNumber = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixNumber() from the server but mixNumber is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixNumber\",\n);\nexport const mixObject = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixObject() from the server but mixObject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixObject\",\n);\nexport const mixVisibility = registerClientReference(\n    function() { throw new Error(\"Attempted to call mixVisibility() from the server but mixVisibility is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"mixVisibility\",\n);\nexport const motion = registerClientReference(\n    function() { throw new Error(\"Attempted to call motion() from the server but motion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"motion\",\n);\nexport const motionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call motionValue() from the server but motionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"motionValue\",\n);\nexport const moveItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call moveItem() from the server but moveItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"moveItem\",\n);\nexport const noop = registerClientReference(\n    function() { throw new Error(\"Attempted to call noop() from the server but noop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"noop\",\n);\nexport const number = registerClientReference(\n    function() { throw new Error(\"Attempted to call number() from the server but number is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"number\",\n);\nexport const numberValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call numberValueTypes() from the server but numberValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"numberValueTypes\",\n);\nexport const observeTimeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call observeTimeline() from the server but observeTimeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"observeTimeline\",\n);\nexport const optimizedAppearDataAttribute = registerClientReference(\n    function() { throw new Error(\"Attempted to call optimizedAppearDataAttribute() from the server but optimizedAppearDataAttribute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"optimizedAppearDataAttribute\",\n);\nexport const parseCSSVariable = registerClientReference(\n    function() { throw new Error(\"Attempted to call parseCSSVariable() from the server but parseCSSVariable is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"parseCSSVariable\",\n);\nexport const parseValueFromTransform = registerClientReference(\n    function() { throw new Error(\"Attempted to call parseValueFromTransform() from the server but parseValueFromTransform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"parseValueFromTransform\",\n);\nexport const percent = registerClientReference(\n    function() { throw new Error(\"Attempted to call percent() from the server but percent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"percent\",\n);\nexport const pipe = registerClientReference(\n    function() { throw new Error(\"Attempted to call pipe() from the server but pipe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"pipe\",\n);\nexport const positionalKeys = registerClientReference(\n    function() { throw new Error(\"Attempted to call positionalKeys() from the server but positionalKeys is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"positionalKeys\",\n);\nexport const press = registerClientReference(\n    function() { throw new Error(\"Attempted to call press() from the server but press is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"press\",\n);\nexport const progress = registerClientReference(\n    function() { throw new Error(\"Attempted to call progress() from the server but progress is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"progress\",\n);\nexport const progressPercentage = registerClientReference(\n    function() { throw new Error(\"Attempted to call progressPercentage() from the server but progressPercentage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"progressPercentage\",\n);\nexport const propEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call propEffect() from the server but propEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"propEffect\",\n);\nexport const px = registerClientReference(\n    function() { throw new Error(\"Attempted to call px() from the server but px is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"px\",\n);\nexport const readTransformValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call readTransformValue() from the server but readTransformValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"readTransformValue\",\n);\nexport const recordStats = registerClientReference(\n    function() { throw new Error(\"Attempted to call recordStats() from the server but recordStats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"recordStats\",\n);\nexport const removeItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call removeItem() from the server but removeItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"removeItem\",\n);\nexport const resize = registerClientReference(\n    function() { throw new Error(\"Attempted to call resize() from the server but resize is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"resize\",\n);\nexport const resolveElements = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveElements() from the server but resolveElements is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"resolveElements\",\n);\nexport const resolveMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveMotionValue() from the server but resolveMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"resolveMotionValue\",\n);\nexport const reverseEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call reverseEasing() from the server but reverseEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"reverseEasing\",\n);\nexport const rgbUnit = registerClientReference(\n    function() { throw new Error(\"Attempted to call rgbUnit() from the server but rgbUnit is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"rgbUnit\",\n);\nexport const rgba = registerClientReference(\n    function() { throw new Error(\"Attempted to call rgba() from the server but rgba is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"rgba\",\n);\nexport const scale = registerClientReference(\n    function() { throw new Error(\"Attempted to call scale() from the server but scale is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"scale\",\n);\nexport const scroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call scroll() from the server but scroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"scroll\",\n);\nexport const scrollInfo = registerClientReference(\n    function() { throw new Error(\"Attempted to call scrollInfo() from the server but scrollInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"scrollInfo\",\n);\nexport const secondsToMilliseconds = registerClientReference(\n    function() { throw new Error(\"Attempted to call secondsToMilliseconds() from the server but secondsToMilliseconds is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"secondsToMilliseconds\",\n);\nexport const setDragLock = registerClientReference(\n    function() { throw new Error(\"Attempted to call setDragLock() from the server but setDragLock is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"setDragLock\",\n);\nexport const setStyle = registerClientReference(\n    function() { throw new Error(\"Attempted to call setStyle() from the server but setStyle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"setStyle\",\n);\nexport const spring = registerClientReference(\n    function() { throw new Error(\"Attempted to call spring() from the server but spring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"spring\",\n);\nexport const springValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call springValue() from the server but springValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"springValue\",\n);\nexport const stagger = registerClientReference(\n    function() { throw new Error(\"Attempted to call stagger() from the server but stagger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"stagger\",\n);\nexport const startOptimizedAppearAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call startOptimizedAppearAnimation() from the server but startOptimizedAppearAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"startOptimizedAppearAnimation\",\n);\nexport const startWaapiAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call startWaapiAnimation() from the server but startWaapiAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"startWaapiAnimation\",\n);\nexport const statsBuffer = registerClientReference(\n    function() { throw new Error(\"Attempted to call statsBuffer() from the server but statsBuffer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"statsBuffer\",\n);\nexport const steps = registerClientReference(\n    function() { throw new Error(\"Attempted to call steps() from the server but steps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"steps\",\n);\nexport const styleEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call styleEffect() from the server but styleEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"styleEffect\",\n);\nexport const supportedWaapiEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportedWaapiEasing() from the server but supportedWaapiEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"supportedWaapiEasing\",\n);\nexport const supportsBrowserAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsBrowserAnimation() from the server but supportsBrowserAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"supportsBrowserAnimation\",\n);\nexport const supportsFlags = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsFlags() from the server but supportsFlags is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"supportsFlags\",\n);\nexport const supportsLinearEasing = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsLinearEasing() from the server but supportsLinearEasing is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"supportsLinearEasing\",\n);\nexport const supportsPartialKeyframes = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsPartialKeyframes() from the server but supportsPartialKeyframes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"supportsPartialKeyframes\",\n);\nexport const supportsScrollTimeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call supportsScrollTimeline() from the server but supportsScrollTimeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"supportsScrollTimeline\",\n);\nexport const svgEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call svgEffect() from the server but svgEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"svgEffect\",\n);\nexport const sync = registerClientReference(\n    function() { throw new Error(\"Attempted to call sync() from the server but sync is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"sync\",\n);\nexport const testValueType = registerClientReference(\n    function() { throw new Error(\"Attempted to call testValueType() from the server but testValueType is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"testValueType\",\n);\nexport const time = registerClientReference(\n    function() { throw new Error(\"Attempted to call time() from the server but time is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"time\",\n);\nexport const transform = registerClientReference(\n    function() { throw new Error(\"Attempted to call transform() from the server but transform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"transform\",\n);\nexport const transformPropOrder = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformPropOrder() from the server but transformPropOrder is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"transformPropOrder\",\n);\nexport const transformProps = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformProps() from the server but transformProps is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"transformProps\",\n);\nexport const transformValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformValue() from the server but transformValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"transformValue\",\n);\nexport const transformValueTypes = registerClientReference(\n    function() { throw new Error(\"Attempted to call transformValueTypes() from the server but transformValueTypes is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"transformValueTypes\",\n);\nexport const unwrapMotionComponent = registerClientReference(\n    function() { throw new Error(\"Attempted to call unwrapMotionComponent() from the server but unwrapMotionComponent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"unwrapMotionComponent\",\n);\nexport const useAnimate = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimate() from the server but useAnimate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useAnimate\",\n);\nexport const useAnimateMini = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimateMini() from the server but useAnimateMini is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useAnimateMini\",\n);\nexport const useAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimation() from the server but useAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useAnimation\",\n);\nexport const useAnimationControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimationControls() from the server but useAnimationControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useAnimationControls\",\n);\nexport const useAnimationFrame = registerClientReference(\n    function() { throw new Error(\"Attempted to call useAnimationFrame() from the server but useAnimationFrame is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useAnimationFrame\",\n);\nexport const useCycle = registerClientReference(\n    function() { throw new Error(\"Attempted to call useCycle() from the server but useCycle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useCycle\",\n);\nexport const useDeprecatedAnimatedState = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDeprecatedAnimatedState() from the server but useDeprecatedAnimatedState is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useDeprecatedAnimatedState\",\n);\nexport const useDeprecatedInvertedScale = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDeprecatedInvertedScale() from the server but useDeprecatedInvertedScale is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useDeprecatedInvertedScale\",\n);\nexport const useDomEvent = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDomEvent() from the server but useDomEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useDomEvent\",\n);\nexport const useDragControls = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDragControls() from the server but useDragControls is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useDragControls\",\n);\nexport const useElementScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useElementScroll() from the server but useElementScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useElementScroll\",\n);\nexport const useForceUpdate = registerClientReference(\n    function() { throw new Error(\"Attempted to call useForceUpdate() from the server but useForceUpdate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useForceUpdate\",\n);\nexport const useInView = registerClientReference(\n    function() { throw new Error(\"Attempted to call useInView() from the server but useInView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useInView\",\n);\nexport const useInstantLayoutTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call useInstantLayoutTransition() from the server but useInstantLayoutTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useInstantLayoutTransition\",\n);\nexport const useInstantTransition = registerClientReference(\n    function() { throw new Error(\"Attempted to call useInstantTransition() from the server but useInstantTransition is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useInstantTransition\",\n);\nexport const useIsPresent = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsPresent() from the server but useIsPresent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useIsPresent\",\n);\nexport const useIsomorphicLayoutEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call useIsomorphicLayoutEffect() from the server but useIsomorphicLayoutEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useIsomorphicLayoutEffect\",\n);\nexport const useMotionTemplate = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMotionTemplate() from the server but useMotionTemplate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useMotionTemplate\",\n);\nexport const useMotionValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMotionValue() from the server but useMotionValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useMotionValue\",\n);\nexport const useMotionValueEvent = registerClientReference(\n    function() { throw new Error(\"Attempted to call useMotionValueEvent() from the server but useMotionValueEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useMotionValueEvent\",\n);\nexport const usePageInView = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePageInView() from the server but usePageInView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"usePageInView\",\n);\nexport const usePresence = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePresence() from the server but usePresence is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"usePresence\",\n);\nexport const usePresenceData = registerClientReference(\n    function() { throw new Error(\"Attempted to call usePresenceData() from the server but usePresenceData is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"usePresenceData\",\n);\nexport const useReducedMotion = registerClientReference(\n    function() { throw new Error(\"Attempted to call useReducedMotion() from the server but useReducedMotion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useReducedMotion\",\n);\nexport const useReducedMotionConfig = registerClientReference(\n    function() { throw new Error(\"Attempted to call useReducedMotionConfig() from the server but useReducedMotionConfig is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useReducedMotionConfig\",\n);\nexport const useResetProjection = registerClientReference(\n    function() { throw new Error(\"Attempted to call useResetProjection() from the server but useResetProjection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useResetProjection\",\n);\nexport const useScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useScroll() from the server but useScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useScroll\",\n);\nexport const useSpring = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSpring() from the server but useSpring is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useSpring\",\n);\nexport const useTime = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTime() from the server but useTime is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useTime\",\n);\nexport const useTransform = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTransform() from the server but useTransform is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useTransform\",\n);\nexport const useUnmountEffect = registerClientReference(\n    function() { throw new Error(\"Attempted to call useUnmountEffect() from the server but useUnmountEffect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useUnmountEffect\",\n);\nexport const useVelocity = registerClientReference(\n    function() { throw new Error(\"Attempted to call useVelocity() from the server but useVelocity is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useVelocity\",\n);\nexport const useViewportScroll = registerClientReference(\n    function() { throw new Error(\"Attempted to call useViewportScroll() from the server but useViewportScroll is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useViewportScroll\",\n);\nexport const useWillChange = registerClientReference(\n    function() { throw new Error(\"Attempted to call useWillChange() from the server but useWillChange is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"useWillChange\",\n);\nexport const velocityPerSecond = registerClientReference(\n    function() { throw new Error(\"Attempted to call velocityPerSecond() from the server but velocityPerSecond is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"velocityPerSecond\",\n);\nexport const vh = registerClientReference(\n    function() { throw new Error(\"Attempted to call vh() from the server but vh is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"vh\",\n);\nexport const visualElementStore = registerClientReference(\n    function() { throw new Error(\"Attempted to call visualElementStore() from the server but visualElementStore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"visualElementStore\",\n);\nexport const vw = registerClientReference(\n    function() { throw new Error(\"Attempted to call vw() from the server but vw is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"vw\",\n);\nexport const warnOnce = registerClientReference(\n    function() { throw new Error(\"Attempted to call warnOnce() from the server but warnOnce is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"warnOnce\",\n);\nexport const warning = registerClientReference(\n    function() { throw new Error(\"Attempted to call warning() from the server but warning is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"warning\",\n);\nexport const wrap = registerClientReference(\n    function() { throw new Error(\"Attempted to call wrap() from the server but wrap is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/framer-motion/dist/es/index.mjs\",\n    \"wrap\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,+BAA+B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9D;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,0DACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,IAAI,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnC;IAAa,MAAM,IAAI,MAAM;AAAkN,GAC/O,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,MAAM,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrC;IAAa,MAAM,IAAI,MAAM;AAAsN,GACnP,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,+BAA+B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9D;IAAa,MAAM,IAAI,MAAM;AAAwQ,GACrS,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,0BAA0B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzD;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,KAAK,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpC;IAAa,MAAM,IAAI,MAAM;AAAoN,GACjP,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,gCAAgC,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/D;IAAa,MAAM,IAAI,MAAM;AAA0Q,GACvS,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,0DACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,0DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,0DACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,0DACA;AAEG,MAAM,uBAAuB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,4BAA4B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3D;IAAa,MAAM,IAAI,MAAM;AAAkQ,GAC/R,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0DACA;AAEG,MAAM,sBAAsB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACrD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,yBAAyB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxD;IAAa,MAAM,IAAI,MAAM;AAA4P,GACzR,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,0DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,0DACA;AAEG,MAAM,oBAAoB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACnD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,0DACA;AAEG,MAAM,KAAK,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpC;IAAa,MAAM,IAAI,MAAM;AAAoN,GACjP,0DACA;AAEG,MAAM,qBAAqB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,0DACA;AAEG,MAAM,KAAK,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACpC;IAAa,MAAM,IAAI,MAAM;AAAoN,GACjP,0DACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,0DACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0DACA;AAEG,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2136, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 2145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/instrumentation/utils.ts"], "sourcesContent": ["export function getRevalidateReason(params: {\n  isOnDemandRevalidate?: boolean\n  isRevalidate?: boolean\n}): 'on-demand' | 'stale' | undefined {\n  if (params.isOnDemandRevalidate) {\n    return 'on-demand'\n  }\n  if (params.isRevalidate) {\n    return 'stale'\n  }\n  return undefined\n}\n"], "names": ["getRevalidateReason", "params", "isOnDemandRevalidate", "isRevalidate", "undefined"], "mappings": ";;;AAAO,SAASA,oBAAoBC,MAGnC;IACC,IAAIA,OAAOC,oBAAoB,EAAE;QAC/B,OAAO;IACT;IACA,IAAID,OAAOE,YAAY,EAAE;QACvB,OAAO;IACT;IACA,OAAOC;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2183, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/app-render/interop-default.ts"], "sourcesContent": ["/**\n * Interop between \"export default\" and \"module.exports\".\n */\nexport function interopDefault(mod: any) {\n  return mod.default || mod\n}\n"], "names": ["interopDefault", "mod", "default"], "mappings": "AAAA;;CAEC,GACD;;;AAAO,SAASA,eAAeC,GAAQ;IACrC,OAAOA,IAAIC,OAAO,IAAID;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2195, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/web/spec-extension/adapters/headers.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n"], "names": ["ReflectAdapter", "ReadonlyHeadersError", "Error", "constructor", "callable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Headers", "headers", "Proxy", "get", "target", "prop", "receiver", "lowercased", "toLowerCase", "original", "Object", "keys", "find", "o", "set", "value", "has", "deleteProperty", "seal", "merge", "Array", "isArray", "join", "from", "append", "name", "existing", "push", "delete", "for<PERSON>ach", "callbackfn", "thisArg", "entries", "call", "key", "values", "Symbol", "iterator"], "mappings": ";;;;AAEA,SAASA,cAAc,QAAQ,YAAW;;AAKnC,MAAMC,6BAA6BC;IACxCC,aAAc;QACZ,KAAK,CACH;IAEJ;IAEA,OAAcC,WAAW;QACvB,MAAM,IAAIH;IACZ;AACF;AAUO,MAAMI,uBAAuBC;IAGlCH,YAAYI,OAA4B,CAAE;QACxC,2EAA2E;QAC3E,2EAA2E;QAC3E,KAAK;QAEL,IAAI,CAACA,OAAO,GAAG,IAAIC,MAAMD,SAAS;YAChCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,sEAAsE;gBACtE,sEAAsE;gBACtE,cAAc;gBACd,IAAI,OAAOD,SAAS,UAAU;oBAC5B,wMAAOX,iBAAAA,CAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC1C;gBAEA,MAAMC,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,0DAA0D;gBAC1D,IAAI,OAAOE,aAAa,aAAa;gBAErC,mDAAmD;gBACnD,wMAAOf,iBAAAA,CAAeS,GAAG,CAACC,QAAQK,UAAUH;YAC9C;YACAQ,KAAIV,MAAM,EAAEC,IAAI,EAAEU,KAAK,EAAET,QAAQ;gBAC/B,IAAI,OAAOD,SAAS,UAAU;oBAC5B,wMAAOX,iBAAAA,CAAeoB,GAAG,CAACV,QAAQC,MAAMU,OAAOT;gBACjD;gBAEA,MAAMC,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,iEAAiE;gBACjE,wMAAOb,iBAAAA,CAAeoB,GAAG,CAACV,QAAQK,YAAYJ,MAAMU,OAAOT;YAC7D;YACAU,KAAIZ,MAAM,EAAEC,IAAI;gBACd,IAAI,OAAOA,SAAS,UAAU,wMAAOX,iBAAAA,CAAesB,GAAG,CAACZ,QAAQC;gBAEhE,MAAME,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,sDAAsD;gBACtD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,8CAA8C;gBAC9C,wMAAOf,iBAAAA,CAAesB,GAAG,CAACZ,QAAQK;YACpC;YACAQ,gBAAeb,MAAM,EAAEC,IAAI;gBACzB,IAAI,OAAOA,SAAS,UAClB,wMAAOX,iBAAAA,CAAeuB,cAAc,CAACb,QAAQC;gBAE/C,MAAME,aAAaF,KAAKG,WAAW;gBAEnC,wEAAwE;gBACxE,qEAAqE;gBACrE,kBAAkB;gBAClB,MAAMC,WAAWC,OAAOC,IAAI,CAACV,SAASW,IAAI,CACxC,CAACC,IAAMA,EAAEL,WAAW,OAAOD;gBAG7B,qDAAqD;gBACrD,IAAI,OAAOE,aAAa,aAAa,OAAO;gBAE5C,sDAAsD;gBACtD,wMAAOf,iBAAAA,CAAeuB,cAAc,CAACb,QAAQK;YAC/C;QACF;IACF;IAEA;;;GAGC,GACD,OAAcS,KAAKjB,OAAgB,EAAmB;QACpD,OAAO,IAAIC,MAAuBD,SAAS;YACzCE,KAAIC,MAAM,EAAEC,IAAI,EAAEC,QAAQ;gBACxB,OAAQD;oBACN,KAAK;oBACL,KAAK;oBACL,KAAK;wBACH,OAAOV,qBAAqBG,QAAQ;oBACtC;wBACE,wMAAOJ,iBAAAA,CAAeS,GAAG,CAACC,QAAQC,MAAMC;gBAC5C;YACF;QACF;IACF;IAEA;;;;;;GAMC,GACOa,MAAMJ,KAAwB,EAAU;QAC9C,IAAIK,MAAMC,OAAO,CAACN,QAAQ,OAAOA,MAAMO,IAAI,CAAC;QAE5C,OAAOP;IACT;IAEA;;;;;GAKC,GACD,OAAcQ,KAAKtB,OAAsC,EAAW;QAClE,IAAIA,mBAAmBD,SAAS,OAAOC;QAEvC,OAAO,IAAIF,eAAeE;IAC5B;IAEOuB,OAAOC,IAAY,EAAEV,KAAa,EAAQ;QAC/C,MAAMW,WAAW,IAAI,CAACzB,OAAO,CAACwB,KAAK;QACnC,IAAI,OAAOC,aAAa,UAAU;YAChC,IAAI,CAACzB,OAAO,CAACwB,KAAK,GAAG;gBAACC;gBAAUX;aAAM;QACxC,OAAO,IAAIK,MAAMC,OAAO,CAACK,WAAW;YAClCA,SAASC,IAAI,CAACZ;QAChB,OAAO;YACL,IAAI,CAACd,OAAO,CAACwB,KAAK,GAAGV;QACvB;IACF;IAEOa,OAAOH,IAAY,EAAQ;QAChC,OAAO,IAAI,CAACxB,OAAO,CAACwB,KAAK;IAC3B;IAEOtB,IAAIsB,IAAY,EAAiB;QACtC,MAAMV,QAAQ,IAAI,CAACd,OAAO,CAACwB,KAAK;QAChC,IAAI,OAAOV,UAAU,aAAa,OAAO,IAAI,CAACI,KAAK,CAACJ;QAEpD,OAAO;IACT;IAEOC,IAAIS,IAAY,EAAW;QAChC,OAAO,OAAO,IAAI,CAACxB,OAAO,CAACwB,KAAK,KAAK;IACvC;IAEOX,IAAIW,IAAY,EAAEV,KAAa,EAAQ;QAC5C,IAAI,CAACd,OAAO,CAACwB,KAAK,GAAGV;IACvB;IAEOc,QACLC,UAAkE,EAClEC,OAAa,EACP;QACN,KAAK,MAAM,CAACN,MAAMV,MAAM,IAAI,IAAI,CAACiB,OAAO,GAAI;YAC1CF,WAAWG,IAAI,CAACF,SAAShB,OAAOU,MAAM,IAAI;QAC5C;IACF;IAEA,CAAQO,UAA6C;QACnD,KAAK,MAAME,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,MAAMwB,OAAOS,IAAI1B,WAAW;YAC5B,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMO,QAAQ,IAAI,CAACZ,GAAG,CAACsB;YAEvB,MAAM;gBAACA;gBAAMV;aAAM;QACrB;IACF;IAEA,CAAQJ,OAAgC;QACtC,KAAK,MAAMuB,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,MAAMwB,OAAOS,IAAI1B,WAAW;YAC5B,MAAMiB;QACR;IACF;IAEA,CAAQU,SAAkC;QACxC,KAAK,MAAMD,OAAOxB,OAAOC,IAAI,CAAC,IAAI,CAACV,OAAO,EAAG;YAC3C,kEAAkE;YAClE,4BAA4B;YAC5B,MAAMc,QAAQ,IAAI,CAACZ,GAAG,CAAC+B;YAEvB,MAAMnB;QACR;IACF;IAEO,CAACqB,OAAOC,QAAQ,CAAC,GAAsC;QAC5D,OAAO,IAAI,CAACL,OAAO;IACrB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2372, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/api-utils/index.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { NextApiResponse } from '../../shared/lib/utils'\n\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../lib/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NodeSpan } from '../lib/trace/constants'\n\nexport type NextApiRequestCookies = Partial<{ [key: string]: string }>\nexport type NextApiRequestQuery = Partial<{ [key: string]: string | string[] }>\n\nexport type __ApiPreviewProps = {\n  previewModeId: string\n  previewModeEncryptionKey: string\n  previewModeSigningKey: string\n}\n\nexport function wrapApiHandler<T extends (...args: any[]) => any>(\n  page: string,\n  handler: T\n): T {\n  return ((...args) => {\n    getTracer().setRootSpanAttribute('next.route', page)\n    // Call API route method\n    return getTracer().trace(\n      NodeSpan.runHandler,\n      {\n        spanName: `executing api route (pages) ${page}`,\n      },\n      () => handler(...args)\n    )\n  }) as T\n}\n\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */\nexport function sendStatusCode(\n  res: NextApiResponse,\n  statusCode: number\n): NextApiResponse<any> {\n  res.statusCode = statusCode\n  return res\n}\n\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */\nexport function redirect(\n  res: NextApiResponse,\n  statusOrUrl: string | number,\n  url?: string\n): NextApiResponse<any> {\n  if (typeof statusOrUrl === 'string') {\n    url = statusOrUrl\n    statusOrUrl = 307\n  }\n  if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n    throw new Error(\n      `Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`\n    )\n  }\n  res.writeHead(statusOrUrl, { Location: url })\n  res.write(url)\n  res.end()\n  return res\n}\n\nexport function checkIsOnDemandRevalidate(\n  req: Request | IncomingMessage | BaseNextRequest,\n  previewProps: __ApiPreviewProps\n): {\n  isOnDemandRevalidate: boolean\n  revalidateOnlyGenerated: boolean\n} {\n  const headers = HeadersAdapter.from(req.headers)\n\n  const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER)\n  const isOnDemandRevalidate = previewModeId === previewProps.previewModeId\n\n  const revalidateOnlyGenerated = headers.has(\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER\n  )\n\n  return { isOnDemandRevalidate, revalidateOnlyGenerated }\n}\n\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`\n\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024\n\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA)\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS)\n\nexport function clearPreviewData<T>(\n  res: NextApiResponse<T>,\n  options: {\n    path?: string\n  } = {}\n): NextApiResponse<T> {\n  if (SYMBOL_CLEARED_COOKIES in res) {\n    return res\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('next/dist/compiled/cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n\n  Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n    value: true,\n    enumerable: false,\n  })\n  return res\n}\n\n/**\n * Custom error class\n */\nexport class ApiError extends Error {\n  readonly statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n    this.statusCode = statusCode\n  }\n}\n\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */\nexport function sendError(\n  res: NextApiResponse,\n  statusCode: number,\n  message: string\n): void {\n  res.statusCode = statusCode\n  res.statusMessage = message\n  res.end(message)\n}\n\ninterface LazyProps {\n  req: IncomingMessage\n}\n\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */\nexport function setLazyProp<T>(\n  { req }: LazyProps,\n  prop: string,\n  getter: () => T\n): void {\n  const opts = { configurable: true, enumerable: true }\n  const optsReset = { ...opts, writable: true }\n\n  Object.defineProperty(req, prop, {\n    ...opts,\n    get: () => {\n      const value = getter()\n      // we set the property on the object to avoid recalculating it\n      Object.defineProperty(req, prop, { ...optsReset, value })\n      return value\n    },\n    set: (value) => {\n      Object.defineProperty(req, prop, { ...optsReset, value })\n    },\n  })\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PRERENDER_REVALIDATE_HEADER", "PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER", "getTracer", "NodeSpan", "wrapApiHandler", "page", "handler", "args", "setRootSpanAttribute", "trace", "<PERSON><PERSON><PERSON><PERSON>", "spanName", "sendStatusCode", "res", "statusCode", "redirect", "statusOrUrl", "url", "Error", "writeHead", "Location", "write", "end", "checkIsOnDemandRevalidate", "req", "previewProps", "headers", "from", "previewModeId", "get", "isOnDemandRevalidate", "revalidateOnlyGenerated", "has", "COOKIE_NAME_PRERENDER_BYPASS", "COOKIE_NAME_PRERENDER_DATA", "RESPONSE_LIMIT_DEFAULT", "SYMBOL_PREVIEW_DATA", "Symbol", "SYMBOL_CLEARED_COOKIES", "clearPreviewData", "options", "serialize", "require", "previous", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "expires", "Date", "httpOnly", "sameSite", "process", "env", "NODE_ENV", "secure", "path", "undefined", "Object", "defineProperty", "value", "enumerable", "ApiError", "constructor", "message", "sendError", "statusMessage", "setLazyProp", "prop", "getter", "opts", "configurable", "optsReset", "writable", "set"], "mappings": ";;;;;;;;;;;;;;;AAKA,SAASA,cAAc,QAAQ,yCAAwC;AACvE,SACEC,2BAA2B,EAC3BC,0CAA0C,QACrC,sBAAqB;AAC5B,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,QAAQ,QAAQ,yBAAwB;;;;;AAW1C,SAASC,eACdC,IAAY,EACZC,OAAU;IAEV,OAAQ,CAAC,GAAGC;oLACVL,YAAAA,IAAYM,oBAAoB,CAAC,cAAcH;QAC/C,wBAAwB;QACxB,OAAOH,wLAAAA,IAAYO,KAAK,4KACtBN,WAAAA,CAASO,UAAU,EACnB;YACEC,UAAU,CAAC,4BAA4B,EAAEN,MAAM;QACjD,GACA,IAAMC,WAAWC;IAErB;AACF;AAOO,SAASK,eACdC,GAAoB,EACpBC,UAAkB;IAElBD,IAAIC,UAAU,GAAGA;IACjB,OAAOD;AACT;AAQO,SAASE,SACdF,GAAoB,EACpBG,WAA4B,EAC5BC,GAAY;IAEZ,IAAI,OAAOD,gBAAgB,UAAU;QACnCC,MAAMD;QACNA,cAAc;IAChB;IACA,IAAI,OAAOA,gBAAgB,YAAY,OAAOC,QAAQ,UAAU;QAC9D,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,qKAAqK,CAAC,GADnK,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IACAL,IAAIM,SAAS,CAACH,aAAa;QAAEI,UAAUH;IAAI;IAC3CJ,IAAIQ,KAAK,CAACJ;IACVJ,IAAIS,GAAG;IACP,OAAOT;AACT;AAEO,SAASU,0BACdC,GAAgD,EAChDC,YAA+B;IAK/B,MAAMC,2MAAU3B,iBAAAA,CAAe4B,IAAI,CAACH,IAAIE,OAAO;IAE/C,MAAME,gBAAgBF,QAAQG,GAAG,yJAAC7B,8BAAAA;IAClC,MAAM8B,uBAAuBF,kBAAkBH,aAAaG,aAAa;IAEzE,MAAMG,0BAA0BL,QAAQM,GAAG,yJACzC/B,6CAAAA;IAGF,OAAO;QAAE6B;QAAsBC;IAAwB;AACzD;AAEO,MAAME,+BAA+B,CAAC,kBAAkB,CAAC,CAAA;AACzD,MAAMC,6BAA6B,CAAC,mBAAmB,CAAC,CAAA;AAExD,MAAMC,yBAAyB,IAAI,OAAO,KAAI;AAE9C,MAAMC,sBAAsBC,OAAOH,4BAA2B;AAC9D,MAAMI,yBAAyBD,OAAOJ,8BAA6B;AAEnE,SAASM,iBACd1B,GAAuB,EACvB2B,UAEI,CAAC,CAAC;IAEN,IAAIF,0BAA0BzB,KAAK;QACjC,OAAOA;IACT;IAEA,MAAM,EAAE4B,SAAS,EAAE,GACjBC,QAAQ;IACV,MAAMC,WAAW9B,IAAI+B,SAAS,CAAC;IAC/B/B,IAAIgC,SAAS,CAAC,CAAC,UAAU,CAAC,EAAE;WACtB,OAAOF,aAAa,WACpB;YAACA;SAAS,GACVG,MAAMC,OAAO,CAACJ,YACZA,WACA,EAAE;QACRF,UAAUR,8BAA8B,IAAI;YAC1C,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEe,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAgB,0BAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,gCAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;QACAhB,UAAUP,4BAA4B,IAAI;YACxC,2DAA2D;YAC3D,oDAAoD;YACpD,wEAAwE;YACxEc,SAAS,IAAIC,KAAK;YAClBC,UAAU;YACVC,UAAUC,QAAQC,GAAG,CAACC,QAAQ,KAAK,aAAgB,0BAAS;YAC5DC,QAAQH,QAAQC,GAAG,CAACC,QAAQ,gCAAK;YACjCE,MAAM;YACN,GAAIhB,QAAQgB,IAAI,KAAKC,YAChB;gBAAED,MAAMhB,QAAQgB,IAAI;YAAC,IACtBC,SAAS;QACf;KACD;IAEDC,OAAOC,cAAc,CAAC9C,KAAKyB,wBAAwB;QACjDsB,OAAO;QACPC,YAAY;IACd;IACA,OAAOhD;AACT;AAKO,MAAMiD,iBAAiB5C;IAG5B6C,YAAYjD,UAAkB,EAAEkD,OAAe,CAAE;QAC/C,KAAK,CAACA;QACN,IAAI,CAAClD,UAAU,GAAGA;IACpB;AACF;AAQO,SAASmD,UACdpD,GAAoB,EACpBC,UAAkB,EAClBkD,OAAe;IAEfnD,IAAIC,UAAU,GAAGA;IACjBD,IAAIqD,aAAa,GAAGF;IACpBnD,IAAIS,GAAG,CAAC0C;AACV;AAYO,SAASG,YACd,EAAE3C,GAAG,EAAa,EAClB4C,IAAY,EACZC,MAAe;IAEf,MAAMC,OAAO;QAAEC,cAAc;QAAMV,YAAY;IAAK;IACpD,MAAMW,YAAY;QAAE,GAAGF,IAAI;QAAEG,UAAU;IAAK;IAE5Cf,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;QAC/B,GAAGE,IAAI;QACPzC,KAAK;YACH,MAAM+B,QAAQS;YACd,8DAA8D;YAC9DX,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;gBAAE,GAAGI,SAAS;gBAAEZ;YAAM;YACvD,OAAOA;QACT;QACAc,KAAK,CAACd;YACJF,OAAOC,cAAc,CAACnC,KAAK4C,MAAM;gBAAE,GAAGI,SAAS;gBAAEZ;YAAM;QACzD;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2528, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/api-utils/get-cookie-parser.ts"], "sourcesContent": ["import type { NextApiRequestCookies } from '.'\n\n/**\n * Parse cookies from the `headers` of request\n * @param req request object\n */\n\nexport function getCookieParser(headers: {\n  [key: string]: string | string[] | null | undefined\n}): () => NextApiRequestCookies {\n  return function parseCookie(): NextApiRequestCookies {\n    const { cookie } = headers\n\n    if (!cookie) {\n      return {}\n    }\n\n    const { parse: parseCookieFn } =\n      require('next/dist/compiled/cookie') as typeof import('next/dist/compiled/cookie')\n    return parseCookieFn(Array.isArray(cookie) ? cookie.join('; ') : cookie)\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "headers", "parse<PERSON><PERSON><PERSON>", "cookie", "parse", "parseCookieFn", "require", "Array", "isArray", "join"], "mappings": "AAEA;;;CAGC,GAED;;;AAAO,SAASA,gBAAgBC,OAE/B;IACC,OAAO,SAASC;QACd,MAAM,EAAEC,MAAM,EAAE,GAAGF;QAEnB,IAAI,CAACE,QAAQ;YACX,OAAO,CAAC;QACV;QAEA,MAAM,EAAEC,OAAOC,aAAa,EAAE,GAC5BC,QAAQ;QACV,OAAOD,cAAcE,MAAMC,OAAO,CAACL,UAAUA,OAAOM,IAAI,CAAC,QAAQN;IACnE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/base-http/index.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'http'\nimport type { I18NConfig } from '../config-shared'\n\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport type { NextApiRequestCookies } from '../api-utils'\nimport { getCookieParser } from '../api-utils/get-cookie-parser'\n\nexport interface BaseNextRequestConfig {\n  basePath: string | undefined\n  i18n?: I18NConfig\n  trailingSlash?: boolean | undefined\n}\n\nexport type FetchMetric = {\n  url: string\n  idx: number\n  end: number\n  start: number\n  method: string\n  status: number\n  cacheReason: string\n  cacheStatus: 'hit' | 'miss' | 'skip' | 'hmr'\n  cacheWarning?: string\n}\n\nexport type FetchMetrics = Array<FetchMetric>\n\nexport abstract class BaseNextRequest<Body = any> {\n  protected _cookies: NextApiRequestCookies | undefined\n  public abstract headers: IncomingHttpHeaders\n  public abstract fetchMetrics: FetchMetric[] | undefined\n\n  constructor(\n    public method: string,\n    public url: string,\n    public body: Body\n  ) {}\n\n  // Utils implemented using the abstract methods above\n\n  public get cookies() {\n    if (this._cookies) return this._cookies\n    return (this._cookies = getCookieParser(this.headers)())\n  }\n}\n\nexport abstract class BaseNextResponse<Destination = any> {\n  abstract statusCode: number | undefined\n  abstract statusMessage: string | undefined\n  abstract get sent(): boolean\n\n  constructor(public destination: Destination) {}\n\n  /**\n   * Sets a value for the header overwriting existing values\n   */\n  abstract setHeader(name: string, value: string | string[]): this\n\n  /**\n   * Removes a header\n   */\n  abstract removeHeader(name: string): this\n\n  /**\n   * Appends value for the given header name\n   */\n  abstract appendHeader(name: string, value: string): this\n\n  /**\n   * Get all values for a header as an array or undefined if no value is present\n   */\n  abstract getHeaderValues(name: string): string[] | undefined\n\n  abstract hasHeader(name: string): boolean\n\n  /**\n   * Get values for a header concatenated using `,` or undefined if no value is present\n   */\n  abstract getHeader(name: string): string | undefined\n\n  abstract getHeaders(): OutgoingHttpHeaders\n\n  abstract body(value: string): this\n\n  abstract send(): void\n\n  abstract onClose(callback: () => void): void\n\n  // Utils implemented using the abstract methods above\n\n  public redirect(destination: string, statusCode: number) {\n    this.setHeader('Location', destination)\n    this.statusCode = statusCode\n\n    // Since IE11 doesn't support the 308 header add backwards\n    // compatibility using refresh header\n    if (statusCode === RedirectStatusCode.PermanentRedirect) {\n      this.setHeader('Refresh', `0;url=${destination}`)\n    }\n\n    return this\n  }\n}\n"], "names": ["RedirectStatusCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BaseNextRequest", "constructor", "method", "url", "body", "cookies", "_cookies", "headers", "BaseNextResponse", "destination", "redirect", "statusCode", "<PERSON><PERSON><PERSON><PERSON>", "PermanentRedirect"], "mappings": ";;;;AAGA,SAASA,kBAAkB,QAAQ,+CAA8C;AAEjF,SAASC,eAAe,QAAQ,iCAAgC;;;AAsBzD,MAAeC;IAKpBC,YACSC,MAAc,EACdC,GAAW,EACXC,IAAU,CACjB;aAHOF,MAAAA,GAAAA;aACAC,GAAAA,GAAAA;aACAC,IAAAA,GAAAA;IACN;IAEH,qDAAqD;IAErD,IAAWC,UAAU;QACnB,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,IAAI,CAACA,QAAQ;QACvC,OAAQ,IAAI,CAACA,QAAQ,gMAAGP,kBAAAA,EAAgB,IAAI,CAACQ,OAAO;IACtD;AACF;AAEO,MAAeC;IAKpBP,YAAmBQ,WAAwB,CAAE;aAA1BA,WAAAA,GAAAA;IAA2B;IAqC9C,qDAAqD;IAE9CC,SAASD,WAAmB,EAAEE,UAAkB,EAAE;QACvD,IAAI,CAACC,SAAS,CAAC,YAAYH;QAC3B,IAAI,CAACE,UAAU,GAAGA;QAElB,0DAA0D;QAC1D,qCAAqC;QACrC,IAAIA,yMAAeb,qBAAAA,CAAmBe,iBAAiB,EAAE;YACvD,IAAI,CAACD,SAAS,CAAC,WAAW,CAAC,MAAM,EAAEH,aAAa;QAClD;QAEA,OAAO,IAAI;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2588, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/base-http/node.ts"], "sourcesContent": ["import type { ServerResponse, IncomingMessage } from 'http'\nimport type { Writable, Readable } from 'stream'\n\nimport { SYMBOL_CLEARED_COOKIES } from '../api-utils'\nimport type { NextApiRequestCookies } from '../api-utils'\n\nimport { NEXT_REQUEST_META } from '../request-meta'\nimport type { RequestMeta } from '../request-meta'\n\nimport { BaseNextRequest, BaseNextResponse, type FetchMetric } from './index'\nimport type { OutgoingHttpHeaders } from 'node:http'\n\ntype Req = IncomingMessage & {\n  [NEXT_REQUEST_META]?: RequestMeta\n  cookies?: NextApiRequestCookies\n  fetchMetrics?: FetchMetric[]\n}\n\nexport class NodeNextRequest extends BaseNextRequest<Readable> {\n  public headers = this._req.headers\n  public fetchMetrics: FetchMetric[] | undefined = this._req?.fetchMetrics;\n\n  [NEXT_REQUEST_META]: RequestMeta = this._req[NEXT_REQUEST_META] || {}\n\n  constructor(private _req: Req) {\n    super(_req.method!.toUpperCase(), _req.url!, _req)\n  }\n\n  get originalRequest() {\n    // Need to mimic these changes to the original req object for places where we use it:\n    // render.tsx, api/ssg requests\n    this._req[NEXT_REQUEST_META] = this[NEXT_REQUEST_META]\n    this._req.url = this.url\n    this._req.cookies = this.cookies\n    return this._req\n  }\n\n  set originalRequest(value: Req) {\n    this._req = value\n  }\n\n  private streaming = false\n\n  /**\n   * Returns the request body as a Web Readable Stream. The body here can only\n   * be read once as the body will start flowing as soon as the data handler\n   * is attached.\n   *\n   * @internal\n   */\n  public stream() {\n    if (this.streaming) {\n      throw new Error(\n        'Invariant: NodeNextRequest.stream() can only be called once'\n      )\n    }\n    this.streaming = true\n\n    return new ReadableStream({\n      start: (controller) => {\n        this._req.on('data', (chunk) => {\n          controller.enqueue(new Uint8Array(chunk))\n        })\n        this._req.on('end', () => {\n          controller.close()\n        })\n        this._req.on('error', (err) => {\n          controller.error(err)\n        })\n      },\n    })\n  }\n}\n\nexport class NodeNextResponse extends BaseNextResponse<Writable> {\n  private textBody: string | undefined = undefined\n\n  public [SYMBOL_CLEARED_COOKIES]?: boolean\n\n  get originalResponse() {\n    if (SYMBOL_CLEARED_COOKIES in this) {\n      this._res[SYMBOL_CLEARED_COOKIES] = this[SYMBOL_CLEARED_COOKIES]\n    }\n\n    return this._res\n  }\n\n  constructor(\n    private _res: ServerResponse & { [SYMBOL_CLEARED_COOKIES]?: boolean }\n  ) {\n    super(_res)\n  }\n\n  get sent() {\n    return this._res.finished || this._res.headersSent\n  }\n\n  get statusCode() {\n    return this._res.statusCode\n  }\n\n  set statusCode(value: number) {\n    this._res.statusCode = value\n  }\n\n  get statusMessage() {\n    return this._res.statusMessage\n  }\n\n  set statusMessage(value: string) {\n    this._res.statusMessage = value\n  }\n\n  setHeader(name: string, value: string | string[]): this {\n    this._res.setHeader(name, value)\n    return this\n  }\n\n  removeHeader(name: string): this {\n    this._res.removeHeader(name)\n    return this\n  }\n\n  getHeaderValues(name: string): string[] | undefined {\n    const values = this._res.getHeader(name)\n\n    if (values === undefined) return undefined\n\n    return (Array.isArray(values) ? values : [values]).map((value) =>\n      value.toString()\n    )\n  }\n\n  hasHeader(name: string): boolean {\n    return this._res.hasHeader(name)\n  }\n\n  getHeader(name: string): string | undefined {\n    const values = this.getHeaderValues(name)\n    return Array.isArray(values) ? values.join(',') : undefined\n  }\n\n  getHeaders(): OutgoingHttpHeaders {\n    return this._res.getHeaders()\n  }\n\n  appendHeader(name: string, value: string): this {\n    const currentValues = this.getHeaderValues(name) ?? []\n\n    if (!currentValues.includes(value)) {\n      this._res.setHeader(name, [...currentValues, value])\n    }\n\n    return this\n  }\n\n  body(value: string) {\n    this.textBody = value\n    return this\n  }\n\n  send() {\n    this._res.end(this.textBody)\n  }\n\n  public onClose(callback: () => void) {\n    this.originalResponse.on('close', callback)\n  }\n}\n"], "names": ["SYMBOL_CLEARED_COOKIES", "NEXT_REQUEST_META", "BaseNextRequest", "BaseNextResponse", "NodeNextRequest", "constructor", "_req", "method", "toUpperCase", "url", "headers", "fetchMetrics", "streaming", "originalRequest", "cookies", "value", "stream", "Error", "ReadableStream", "start", "controller", "on", "chunk", "enqueue", "Uint8Array", "close", "err", "error", "NodeNextResponse", "originalResponse", "_res", "textBody", "undefined", "sent", "finished", "headersSent", "statusCode", "statusMessage", "<PERSON><PERSON><PERSON><PERSON>", "name", "removeHeader", "getHeader<PERSON><PERSON>ues", "values", "<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "map", "toString", "<PERSON><PERSON><PERSON><PERSON>", "join", "getHeaders", "append<PERSON><PERSON>er", "currentV<PERSON>ues", "includes", "body", "send", "end", "onClose", "callback"], "mappings": ";;;;AAGA,SAASA,sBAAsB,QAAQ,eAAc;AAGrD,SAASC,iBAAiB,QAAQ,kBAAiB;AAGnD,SAASC,eAAe,EAAEC,gBAAgB,QAA0B,UAAS;;;;;AAStE,MAAMC,+LAAwBF,kBAAAA;uBAIlCD,sLAAAA,oBAAAA,CAAAA;IAEDI,YAAoBC,IAAS,CAAE;YAJkB;QAK/C,KAAK,CAACA,KAAKC,MAAM,CAAEC,WAAW,IAAIF,KAAKG,GAAG,EAAGH,OAAAA,IAAAA,CAD3BA,IAAAA,GAAAA,MAAAA,IAAAA,CALbI,OAAAA,GAAU,IAAI,CAACJ,IAAI,CAACI,OAAO,EAAA,IAAA,CAC3BC,YAAAA,GAAAA,CAA0C,aAAA,IAAI,CAACL,IAAI,KAAA,OAAA,KAAA,IAAT,WAAWK,YAAY,EAAA,IAExE,CAACV,mBAAkB,GAAgB,IAAI,CAACK,IAAI,kKAACL,oBAAAA,CAAkB,IAAI,CAAC,GAAA,IAAA,CAmB5DW,SAAAA,GAAY;IAfpB;IAEA,IAAIC,kBAAkB;QACpB,qFAAqF;QACrF,+BAA+B;QAC/B,IAAI,CAACP,IAAI,kKAACL,oBAAAA,CAAkB,GAAG,IAAI,kKAACA,oBAAAA,CAAkB;QACtD,IAAI,CAACK,IAAI,CAACG,GAAG,GAAG,IAAI,CAACA,GAAG;QACxB,IAAI,CAACH,IAAI,CAACQ,OAAO,GAAG,IAAI,CAACA,OAAO;QAChC,OAAO,IAAI,CAACR,IAAI;IAClB;IAEA,IAAIO,gBAAgBE,KAAU,EAAE;QAC9B,IAAI,CAACT,IAAI,GAAGS;IACd;IAIA;;;;;;GAMC,GACMC,SAAS;QACd,IAAI,IAAI,CAACJ,SAAS,EAAE;YAClB,MAAM,OAAA,cAEL,CAFK,IAAIK,MACR,gEADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAI,CAACL,SAAS,GAAG;QAEjB,OAAO,IAAIM,eAAe;YACxBC,OAAO,CAACC;gBACN,IAAI,CAACd,IAAI,CAACe,EAAE,CAAC,QAAQ,CAACC;oBACpBF,WAAWG,OAAO,CAAC,IAAIC,WAAWF;gBACpC;gBACA,IAAI,CAAChB,IAAI,CAACe,EAAE,CAAC,OAAO;oBAClBD,WAAWK,KAAK;gBAClB;gBACA,IAAI,CAACnB,IAAI,CAACe,EAAE,CAAC,SAAS,CAACK;oBACrBN,WAAWO,KAAK,CAACD;gBACnB;YACF;QACF;IACF;AACF;AAEO,MAAME,gMAAyBzB,mBAAAA;IAKpC,IAAI0B,mBAAmB;QACrB,2KAAI7B,yBAAAA,IAA0B,IAAI,EAAE;YAClC,IAAI,CAAC8B,IAAI,wKAAC9B,yBAAAA,CAAuB,GAAG,IAAI,wKAACA,yBAAAA,CAAuB;QAClE;QAEA,OAAO,IAAI,CAAC8B,IAAI;IAClB;IAEAzB,YACUyB,IAA6D,CACrE;QACA,KAAK,CAACA,OAAAA,IAAAA,CAFEA,IAAAA,GAAAA,MAAAA,IAAAA,CAbFC,QAAAA,GAA+BC;IAgBvC;IAEA,IAAIC,OAAO;QACT,OAAO,IAAI,CAACH,IAAI,CAACI,QAAQ,IAAI,IAAI,CAACJ,IAAI,CAACK,WAAW;IACpD;IAEA,IAAIC,aAAa;QACf,OAAO,IAAI,CAACN,IAAI,CAACM,UAAU;IAC7B;IAEA,IAAIA,WAAWrB,KAAa,EAAE;QAC5B,IAAI,CAACe,IAAI,CAACM,UAAU,GAAGrB;IACzB;IAEA,IAAIsB,gBAAgB;QAClB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa;IAChC;IAEA,IAAIA,cAActB,KAAa,EAAE;QAC/B,IAAI,CAACe,IAAI,CAACO,aAAa,GAAGtB;IAC5B;IAEAuB,UAAUC,IAAY,EAAExB,KAAwB,EAAQ;QACtD,IAAI,CAACe,IAAI,CAACQ,SAAS,CAACC,MAAMxB;QAC1B,OAAO,IAAI;IACb;IAEAyB,aAAaD,IAAY,EAAQ;QAC/B,IAAI,CAACT,IAAI,CAACU,YAAY,CAACD;QACvB,OAAO,IAAI;IACb;IAEAE,gBAAgBF,IAAY,EAAwB;QAClD,MAAMG,SAAS,IAAI,CAACZ,IAAI,CAACa,SAAS,CAACJ;QAEnC,IAAIG,WAAWV,WAAW,OAAOA;QAEjC,OAAQY,CAAAA,MAAMC,OAAO,CAACH,UAAUA,SAAS;YAACA;SAAM,EAAGI,GAAG,CAAC,CAAC/B,QACtDA,MAAMgC,QAAQ;IAElB;IAEAC,UAAUT,IAAY,EAAW;QAC/B,OAAO,IAAI,CAACT,IAAI,CAACkB,SAAS,CAACT;IAC7B;IAEAI,UAAUJ,IAAY,EAAsB;QAC1C,MAAMG,SAAS,IAAI,CAACD,eAAe,CAACF;QACpC,OAAOK,MAAMC,OAAO,CAACH,UAAUA,OAAOO,IAAI,CAAC,OAAOjB;IACpD;IAEAkB,aAAkC;QAChC,OAAO,IAAI,CAACpB,IAAI,CAACoB,UAAU;IAC7B;IAEAC,aAAaZ,IAAY,EAAExB,KAAa,EAAQ;QAC9C,MAAMqC,gBAAgB,IAAI,CAACX,eAAe,CAACF,SAAS,EAAE;QAEtD,IAAI,CAACa,cAAcC,QAAQ,CAACtC,QAAQ;YAClC,IAAI,CAACe,IAAI,CAACQ,SAAS,CAACC,MAAM;mBAAIa;gBAAerC;aAAM;QACrD;QAEA,OAAO,IAAI;IACb;IAEAuC,KAAKvC,KAAa,EAAE;QAClB,IAAI,CAACgB,QAAQ,GAAGhB;QAChB,OAAO,IAAI;IACb;IAEAwC,OAAO;QACL,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAAC,IAAI,CAACzB,QAAQ;IAC7B;IAEO0B,QAAQC,QAAoB,EAAE;QACnC,IAAI,CAAC7B,gBAAgB,CAACR,EAAE,CAAC,SAASqC;IACpC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2722, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/lib/experimental/ppr.ts"], "sourcesContent": ["/**\n * If set to `incremental`, only those leaf pages that export\n * `experimental_ppr = true` will have partial prerendering enabled. If any\n * page exports this value as `false` or does not export it at all will not\n * have partial prerendering enabled. If set to a boolean, the options for\n * `experimental_ppr` will be ignored.\n */\n\nexport type ExperimentalPPRConfig = boolean | 'incremental'\n\n/**\n * Returns true if partial prerendering is enabled for the application. It does\n * not tell you if a given route has PPR enabled, as that requires analysis of\n * the route's configuration.\n *\n * @see {@link checkIsRoutePPREnabled} - for checking if a specific route has PPR enabled.\n */\nexport function checkIsAppPPREnabled(\n  config: ExperimentalPPRConfig | undefined\n): boolean {\n  // If the config is undefined, partial prerendering is disabled.\n  if (typeof config === 'undefined') return false\n\n  // If the config is a boolean, use it directly.\n  if (typeof config === 'boolean') return config\n\n  // If the config is a string, it must be 'incremental' to enable partial\n  // prerendering.\n  if (config === 'incremental') return true\n\n  return false\n}\n\n/**\n * Returns true if partial prerendering is supported for the current page with\n * the provided app configuration. If the application doesn't have partial\n * prerendering enabled, this function will always return false. If you want to\n * check if the application has partial prerendering enabled\n *\n * @see {@link checkIsAppPPREnabled} for checking if the application has PPR enabled.\n */\nexport function checkIsRoutePPREnabled(\n  config: ExperimentalPPRConfig | undefined,\n  appConfig: {\n    experimental_ppr?: boolean\n  }\n): boolean {\n  // If the config is undefined, partial prerendering is disabled.\n  if (typeof config === 'undefined') return false\n\n  // If the config is a boolean, use it directly.\n  if (typeof config === 'boolean') return config\n\n  // If the config is a string, it must be 'incremental' to enable partial\n  // prerendering.\n  if (config === 'incremental' && appConfig.experimental_ppr === true) {\n    return true\n  }\n\n  return false\n}\n"], "names": ["checkIsAppPPREnabled", "config", "checkIsRoutePPREnabled", "appConfig", "experimental_ppr"], "mappings": "AAAA;;;;;;CAMC,GAID;;;;;;CAMC,GACD;;;;AAAO,SAASA,qBACdC,MAAyC;IAEzC,gEAAgE;IAChE,IAAI,OAAOA,WAAW,aAAa,OAAO;IAE1C,+CAA+C;IAC/C,IAAI,OAAOA,WAAW,WAAW,OAAOA;IAExC,wEAAwE;IACxE,gBAAgB;IAChB,IAAIA,WAAW,eAAe,OAAO;IAErC,OAAO;AACT;AAUO,SAASC,uBACdD,MAAyC,EACzCE,SAEC;IAED,gEAAgE;IAChE,IAAI,OAAOF,WAAW,aAAa,OAAO;IAE1C,+CAA+C;IAC/C,IAAI,OAAOA,WAAW,WAAW,OAAOA;IAExC,wEAAwE;IACxE,gBAAgB;IAChB,IAAIA,WAAW,iBAAiBE,UAAUC,gBAAgB,KAAK,MAAM;QACnE,OAAO;IACT;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2764, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["WEB_VITALS", "execOnce", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "isAbsoluteUrl", "url", "test", "getLocationOrigin", "protocol", "hostname", "port", "window", "location", "getURL", "href", "origin", "substring", "length", "getDisplayName", "Component", "displayName", "name", "isResSent", "res", "finished", "headersSent", "normalizeRepeatedSlashes", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "loadGetInitialProps", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "SP", "performance", "ST", "every", "method", "DecodeError", "NormalizeError", "PageNotFoundError", "constructor", "page", "code", "MissingStaticPage", "MiddlewareNotFoundError", "stringifyError", "error", "JSON", "stringify", "stack"], "mappings": "AAwCA;;;CAGC,GACD;;;;;;;;;;;;;;;;;;;AAAO,MAAMA,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO,CAAS;AAsQvE,SAASC,SACdC,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMC,gBAAgB,CAACC,MAAgBF,mBAAmBG,IAAI,CAACD,KAAI;AAEnE,SAASE;IACd,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASG;IACd,MAAM,EAAEC,IAAI,EAAE,GAAGH,OAAOC,QAAQ;IAChC,MAAMG,SAASR;IACf,OAAOO,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASC,eAAkBC,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAASC,UAAUC,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASC,yBAAyBrB,GAAW;IAClD,MAAMsB,WAAWtB,IAAIuB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAeC,oBAIpBC,GAAgC,EAAEC,GAAM;IACxC,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;YACrCJ;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAGvB,eAClBgB,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMlB,MAAMY,IAAIZ,GAAG,IAAKY,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACZ,GAAG;IAE9C,IAAI,CAACW,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIhB,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLwB,WAAW,MAAMV,oBAAoBE,IAAIhB,SAAS,EAAEgB,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIZ,OAAOD,UAAUC,MAAM;QACzB,OAAOqB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAGvB,eAClBgB,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAO3B,MAAM,KAAK,KAAK,CAACkB,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAE9B,eACDgB,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMK,KAAK,OAAOC,gBAAgB,YAAW;AAC7C,MAAMC,KACXF,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWG,KAAK,CACtD,CAACC,SAAW,OAAOH,WAAW,CAACG,OAAO,KAAK,YAC5C;AAEI,MAAMC,oBAAoBZ;AAAO;AACjC,MAAMa,uBAAuBb;AAAO;AACpC,MAAMc,0BAA0Bd;IAGrCe,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAACtC,IAAI,GAAG;QACZ,IAAI,CAACoB,OAAO,GAAI,kCAA+BiB;IACjD;AACF;AAEO,MAAME,0BAA0BlB;IACrCe,YAAYC,IAAY,EAAEjB,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCiB,OAAK,MAAGjB;IACjE;AACF;AAEO,MAAMoB,gCAAgCnB;IAE3Ce,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAAClB,OAAO,GAAI;IAClB;AACF;AAWO,SAASqB,eAAeC,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAExB,SAASsB,MAAMtB,OAAO;QAAEyB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2917, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/router/utils/route-matcher.ts"], "sourcesContent": ["import type { Group } from './route-regex'\nimport { DecodeError } from '../../utils'\nimport type { Params } from '../../../../server/request/params'\n\nexport interface RouteMatchFn {\n  (pathname: string): false | Params\n}\n\ntype RouteMatcherOptions = {\n  // We only use the exec method of the RegExp object. This helps us avoid using\n  // type assertions that the passed in properties are of the correct type.\n  re: Pick<RegExp, 'exec'>\n  groups: Record<string, Group>\n}\n\nexport function getRouteMatcher({\n  re,\n  groups,\n}: RouteMatcherOptions): RouteMatchFn {\n  return (pathname: string) => {\n    const routeMatch = re.exec(pathname)\n    if (!routeMatch) return false\n\n    const decode = (param: string) => {\n      try {\n        return decodeURIComponent(param)\n      } catch {\n        throw new DecodeError('failed to decode param')\n      }\n    }\n\n    const params: Params = {}\n    for (const [key, group] of Object.entries(groups)) {\n      const match = routeMatch[group.pos]\n      if (match !== undefined) {\n        if (group.repeat) {\n          params[key] = match.split('/').map((entry) => decode(entry))\n        } else {\n          params[key] = decode(match)\n        }\n      }\n    }\n\n    return params\n  }\n}\n"], "names": ["DecodeError", "getRouteMatcher", "re", "groups", "pathname", "routeMatch", "exec", "decode", "param", "decodeURIComponent", "params", "key", "group", "Object", "entries", "match", "pos", "undefined", "repeat", "split", "map", "entry"], "mappings": ";;;AACA,SAASA,WAAW,QAAQ,cAAa;;AAclC,SAASC,gBAAgB,KAGV;IAHU,IAAA,EAC9BC,EAAE,EACFC,MAAM,EACc,GAHU;IAI9B,OAAO,CAACC;QACN,MAAMC,aAAaH,GAAGI,IAAI,CAACF;QAC3B,IAAI,CAACC,YAAY,OAAO;QAExB,MAAME,SAAS,CAACC;YACd,IAAI;gBACF,OAAOC,mBAAmBD;YAC5B,EAAE,OAAA,GAAM;gBACN,MAAM,OAAA,cAAyC,CAAzC,kKAAIR,cAAAA,CAAY,2BAAhB,qBAAA;2BAAA;gCAAA;kCAAA;gBAAwC;YAChD;QACF;QAEA,MAAMU,SAAiB,CAAC;QACxB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIC,OAAOC,OAAO,CAACX,QAAS;YACjD,MAAMY,QAAQV,UAAU,CAACO,MAAMI,GAAG,CAAC;YACnC,IAAID,UAAUE,WAAW;gBACvB,IAAIL,MAAMM,MAAM,EAAE;oBAChBR,MAAM,CAACC,IAAI,GAAGI,MAAMI,KAAK,CAAC,KAAKC,GAAG,CAAC,CAACC,QAAUd,OAAOc;gBACvD,OAAO;oBACLX,MAAM,CAACC,IAAI,GAAGJ,OAAOQ;gBACvB;YACF;QACF;QAEA,OAAOL;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2956, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/page-path/ensure-leading-slash.ts"], "sourcesContent": ["/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n"], "names": ["ensureLeadingSlash", "path", "startsWith"], "mappings": "AAAA;;;CAGC,GACD;;;AAAO,SAASA,mBAAmBC,IAAY;IAC7C,OAAOA,KAAKC,UAAU,CAAC,OAAOD,OAAQ,MAAGA;AAC3C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2969, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/router/utils/app-paths.ts"], "sourcesContent": ["import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n"], "names": ["ensureLeadingSlash", "isGroupSegment", "normalizeAppPath", "route", "split", "reduce", "pathname", "segment", "index", "segments", "length", "normalizeRscURL", "url", "replace"], "mappings": ";;;;AAAA,SAASA,kBAAkB,QAAQ,uCAAsC;AACzE,SAASC,cAAc,QAAQ,gBAAe;;;AAqBvC,SAASC,iBAAiBC,KAAa;IAC5C,8MAAOH,qBAAAA,EACLG,MAAMC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,UAAUC,SAASC,OAAOC;QACjD,8BAA8B;QAC9B,IAAI,CAACF,SAAS;YACZ,OAAOD;QACT;QAEA,sBAAsB;QACtB,wKAAIL,iBAAAA,EAAeM,UAAU;YAC3B,OAAOD;QACT;QAEA,iCAAiC;QACjC,IAAIC,OAAO,CAAC,EAAE,KAAK,KAAK;YACtB,OAAOD;QACT;QAEA,uDAAuD;QACvD,IACGC,CAAAA,YAAY,UAAUA,YAAY,OAAM,KACzCC,UAAUC,SAASC,MAAM,GAAG,GAC5B;YACA,OAAOJ;QACT;QAEA,OAAUA,WAAS,MAAGC;IACxB,GAAG;AAEP;AAMO,SAASI,gBAAgBC,GAAW;IACzC,OAAOA,IAAIC,OAAO,CAChB,eACA,AACA,8BAD8B;AAGlC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3005, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/router/utils/interception-routes.ts"], "sourcesContent": ["import { normalizeAppPath } from './app-paths'\n\n// order matters here, the first match will be used\nexport const INTERCEPTION_ROUTE_MARKERS = [\n  '(..)(..)',\n  '(.)',\n  '(..)',\n  '(...)',\n] as const\n\nexport function isInterceptionRouteAppPath(path: string): boolean {\n  // TODO-APP: add more serious validation\n  return (\n    path\n      .split('/')\n      .find((segment) =>\n        INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n      ) !== undefined\n  )\n}\n\nexport function extractInterceptionRouteInformation(path: string) {\n  let interceptingRoute: string | undefined,\n    marker: (typeof INTERCEPTION_ROUTE_MARKERS)[number] | undefined,\n    interceptedRoute: string | undefined\n\n  for (const segment of path.split('/')) {\n    marker = INTERCEPTION_ROUTE_MARKERS.find((m) => segment.startsWith(m))\n    if (marker) {\n      ;[interceptingRoute, interceptedRoute] = path.split(marker, 2)\n      break\n    }\n  }\n\n  if (!interceptingRoute || !marker || !interceptedRoute) {\n    throw new Error(\n      `Invalid interception route: ${path}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`\n    )\n  }\n\n  interceptingRoute = normalizeAppPath(interceptingRoute) // normalize the path, e.g. /(blog)/feed -> /feed\n\n  switch (marker) {\n    case '(.)':\n      // (.) indicates that we should match with sibling routes, so we just need to append the intercepted route to the intercepting route\n      if (interceptingRoute === '/') {\n        interceptedRoute = `/${interceptedRoute}`\n      } else {\n        interceptedRoute = interceptingRoute + '/' + interceptedRoute\n      }\n      break\n    case '(..)':\n      // (..) indicates that we should match at one level up, so we need to remove the last segment of the intercepting route\n      if (interceptingRoute === '/') {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..) marker at the root level, use (.) instead.`\n        )\n      }\n      interceptedRoute = interceptingRoute\n        .split('/')\n        .slice(0, -1)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    case '(...)':\n      // (...) will match the route segment in the root directory, so we need to use the root directory to prepend the intercepted route\n      interceptedRoute = '/' + interceptedRoute\n      break\n    case '(..)(..)':\n      // (..)(..) indicates that we should match at two levels up, so we need to remove the last two segments of the intercepting route\n\n      const splitInterceptingRoute = interceptingRoute.split('/')\n      if (splitInterceptingRoute.length <= 2) {\n        throw new Error(\n          `Invalid interception route: ${path}. Cannot use (..)(..) marker at the root level or one level up.`\n        )\n      }\n\n      interceptedRoute = splitInterceptingRoute\n        .slice(0, -2)\n        .concat(interceptedRoute)\n        .join('/')\n      break\n    default:\n      throw new Error('Invariant: unexpected marker')\n  }\n\n  return { interceptingRoute, interceptedRoute }\n}\n"], "names": ["normalizeAppPath", "INTERCEPTION_ROUTE_MARKERS", "isInterceptionRouteAppPath", "path", "split", "find", "segment", "m", "startsWith", "undefined", "extractInterceptionRouteInformation", "interceptingRoute", "marker", "interceptedRoute", "Error", "slice", "concat", "join", "splitInterceptingRoute", "length"], "mappings": ";;;;;AAAA,SAASA,gBAAgB,QAAQ,cAAa;;AAGvC,MAAMC,6BAA6B;IACxC;IACA;IACA;IACA;CACD,CAAS;AAEH,SAASC,2BAA2BC,IAAY;IACrD,wCAAwC;IACxC,OACEA,KACGC,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UACLL,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD,SACtDE;AAEZ;AAEO,SAASC,oCAAoCP,IAAY;IAC9D,IAAIQ,mBACFC,QACAC;IAEF,KAAK,MAAMP,WAAWH,KAAKC,KAAK,CAAC,KAAM;QACrCQ,SAASX,2BAA2BI,IAAI,CAAC,CAACE,IAAMD,QAAQE,UAAU,CAACD;QACnE,IAAIK,QAAQ;;YACT,CAACD,mBAAmBE,iBAAiB,GAAGV,KAAKC,KAAK,CAACQ,QAAQ;YAC5D;QACF;IACF;IAEA,IAAI,CAACD,qBAAqB,CAACC,UAAU,CAACC,kBAAkB;QACtD,MAAM,OAAA,cAEL,CAFK,IAAIC,MACP,iCAA8BX,OAAK,sFADhC,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEAQ,gNAAoBX,mBAAAA,EAAiBW,mBAAmB,iDAAiD;;IAEzG,OAAQC;QACN,KAAK;YACH,oIAAoI;YACpI,IAAID,sBAAsB,KAAK;gBAC7BE,mBAAoB,MAAGA;YACzB,OAAO;gBACLA,mBAAmBF,oBAAoB,MAAME;YAC/C;YACA;QACF,KAAK;YACH,uHAAuH;YACvH,IAAIF,sBAAsB,KAAK;gBAC7B,MAAM,OAAA,cAEL,CAFK,IAAIG,MACP,iCAA8BX,OAAK,iEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAU,mBAAmBF,kBAChBP,KAAK,CAAC,KACNW,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF,KAAK;YACH,kIAAkI;YAClIJ,mBAAmB,MAAMA;YACzB;QACF,KAAK;YACH,iIAAiI;YAEjI,MAAMK,yBAAyBP,kBAAkBP,KAAK,CAAC;YACvD,IAAIc,uBAAuBC,MAAM,IAAI,GAAG;gBACtC,MAAM,OAAA,cAEL,CAFK,IAAIL,MACP,iCAA8BX,OAAK,oEADhC,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAU,mBAAmBK,uBAChBH,KAAK,CAAC,GAAG,CAAC,GACVC,MAAM,CAACH,kBACPI,IAAI,CAAC;YACR;QACF;YACE,MAAM,OAAA,cAAyC,CAAzC,IAAIH,MAAM,iCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwC;IAClD;IAEA,OAAO;QAAEH;QAAmBE;IAAiB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3093, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/escape-regexp.ts"], "sourcesContent": ["// regexp is based on https://github.com/sindresorhus/escape-string-regexp\nconst reHasRegExp = /[|\\\\{}()[\\]^$+*?.-]/\nconst reReplaceRegExp = /[|\\\\{}()[\\]^$+*?.-]/g\n\nexport function escapeStringRegexp(str: string) {\n  // see also: https://github.com/lodash/lodash/blob/2da024c3b4f9947a48517639de7560457cd4ec6c/escapeRegExp.js#L23\n  if (reHasRegExp.test(str)) {\n    return str.replace(reReplaceRegExp, '\\\\$&')\n  }\n  return str\n}\n"], "names": ["reHasRegExp", "reReplaceRegExp", "escapeStringRegexp", "str", "test", "replace"], "mappings": "AAAA,0EAA0E;;;;AAC1E,MAAMA,cAAc;AACpB,MAAMC,kBAAkB;AAEjB,SAASC,mBAAmBC,GAAW;IAC5C,+GAA+G;IAC/G,IAAIH,YAAYI,IAAI,CAACD,MAAM;QACzB,OAAOA,IAAIE,OAAO,CAACJ,iBAAiB;IACtC;IACA,OAAOE;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3110, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/router/utils/route-regex.ts"], "sourcesContent": ["import {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../../../lib/constants'\nimport { INTERCEPTION_ROUTE_MARKERS } from './interception-routes'\nimport { escapeStringRegexp } from '../../escape-regexp'\nimport { removeTrailingSlash } from './remove-trailing-slash'\n\nexport interface Group {\n  pos: number\n  repeat: boolean\n  optional: boolean\n}\n\nexport interface RouteRegex {\n  groups: { [groupName: string]: Group }\n  re: RegExp\n}\n\ntype GetNamedRouteRegexOptions = {\n  /**\n   * Whether to prefix the route keys with the NEXT_INTERCEPTION_MARKER_PREFIX\n   * or NEXT_QUERY_PARAM_PREFIX. This is only relevant when creating the\n   * routes-manifest during the build.\n   */\n  prefixRouteKeys: boolean\n\n  /**\n   * Whether to include the suffix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n\n  /**\n   * Whether to backtrack duplicate keys. This is only relevant when creating\n   * the routes-manifest during the build.\n   */\n  backreferenceDuplicateKeys?: boolean\n}\n\ntype GetRouteRegexOptions = {\n  /**\n   * Whether to include extra parts in the route regex. This means that when you\n   * have something like `/[...slug].json` the `.json` part will be included\n   * in the regex, yielding `/(.*).json` as the regex.\n   */\n  includeSuffix?: boolean\n\n  /**\n   * Whether to include the prefix in the route regex. This means that when you\n   * have something like `/[...slug].json` the `/` part will be included\n   * in the regex, yielding `^/(.*).json$` as the regex.\n   *\n   * Note that interception markers will already be included without the need\n   * of adding this option.\n   */\n  includePrefix?: boolean\n\n  /**\n   * Whether to exclude the optional trailing slash from the route regex.\n   */\n  excludeOptionalTrailingSlash?: boolean\n}\n\n/**\n * Regular expression pattern used to match route parameters.\n * Matches both single parameters and parameter groups.\n * Examples:\n *   - `[[...slug]]` matches parameter group with key 'slug', repeat: true, optional: true\n *   - `[...slug]` matches parameter group with key 'slug', repeat: true, optional: false\n *   - `[[foo]]` matches parameter with key 'foo', repeat: false, optional: true\n *   - `[bar]` matches parameter with key 'bar', repeat: false, optional: false\n */\nconst PARAMETER_PATTERN = /^([^[]*)\\[((?:\\[[^\\]]*\\])|[^\\]]+)\\](.*)$/\n\n/**\n * Parses a given parameter from a route to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[[...slug]]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[[foo]]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `[bar]` -> `{ key: 'bar', repeat: false, optional: false }`\n *   - `fizz` -> `{ key: 'fizz', repeat: false, optional: false }`\n * @param param - The parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nexport function parseParameter(param: string) {\n  const match = param.match(PARAMETER_PATTERN)\n\n  if (!match) {\n    return parseMatchedParameter(param)\n  }\n\n  return parseMatchedParameter(match[2])\n}\n\n/**\n * Parses a matched parameter from the PARAMETER_PATTERN regex to a data structure that can be used\n * to generate the parametrized route.\n * Examples:\n *   - `[...slug]` -> `{ key: 'slug', repeat: true, optional: true }`\n *   - `...slug` -> `{ key: 'slug', repeat: true, optional: false }`\n *   - `[foo]` -> `{ key: 'foo', repeat: false, optional: true }`\n *   - `bar` -> `{ key: 'bar', repeat: false, optional: false }`\n * @param param - The matched parameter to parse.\n * @returns The parsed parameter as a data structure.\n */\nfunction parseMatchedParameter(param: string) {\n  const optional = param.startsWith('[') && param.endsWith(']')\n  if (optional) {\n    param = param.slice(1, -1)\n  }\n  const repeat = param.startsWith('...')\n  if (repeat) {\n    param = param.slice(3)\n  }\n  return { key: param, repeat, optional }\n}\n\nfunction getParametrizedRoute(\n  route: string,\n  includeSuffix: boolean,\n  includePrefix: boolean\n) {\n  const groups: { [groupName: string]: Group } = {}\n  let groupIndex = 1\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const markerMatch = INTERCEPTION_ROUTE_MARKERS.find((m) =>\n      segment.startsWith(m)\n    )\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (markerMatch && paramMatches && paramMatches[2]) {\n      const { key, optional, repeat } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n      segments.push(`/${escapeStringRegexp(markerMatch)}([^/]+?)`)\n    } else if (paramMatches && paramMatches[2]) {\n      const { key, repeat, optional } = parseMatchedParameter(paramMatches[2])\n      groups[key] = { pos: groupIndex++, repeat, optional }\n\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = repeat ? (optional ? '(?:/(.+?))?' : '/(.+?)') : '/([^/]+?)'\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    parameterizedRoute: segments.join(''),\n    groups,\n  }\n}\n\n/**\n * From a normalized route this function generates a regular expression and\n * a corresponding groups object intended to be used to store matching groups\n * from the regular expression.\n */\nexport function getRouteRegex(\n  normalizedRoute: string,\n  {\n    includeSuffix = false,\n    includePrefix = false,\n    excludeOptionalTrailingSlash = false,\n  }: GetRouteRegexOptions = {}\n): RouteRegex {\n  const { parameterizedRoute, groups } = getParametrizedRoute(\n    normalizedRoute,\n    includeSuffix,\n    includePrefix\n  )\n\n  let re = parameterizedRoute\n  if (!excludeOptionalTrailingSlash) {\n    re += '(?:/)?'\n  }\n\n  return {\n    re: new RegExp(`^${re}$`),\n    groups: groups,\n  }\n}\n\n/**\n * Builds a function to generate a minimal routeKey using only a-z and minimal\n * number of characters.\n */\nfunction buildGetSafeRouteKey() {\n  let i = 0\n\n  return () => {\n    let routeKey = ''\n    let j = ++i\n    while (j > 0) {\n      routeKey += String.fromCharCode(97 + ((j - 1) % 26))\n      j = Math.floor((j - 1) / 26)\n    }\n    return routeKey\n  }\n}\n\nfunction getSafeKeyFromSegment({\n  interceptionMarker,\n  getSafeRouteKey,\n  segment,\n  routeKeys,\n  keyPrefix,\n  backreferenceDuplicateKeys,\n}: {\n  interceptionMarker?: string\n  getSafeRouteKey: () => string\n  segment: string\n  routeKeys: Record<string, string>\n  keyPrefix?: string\n  backreferenceDuplicateKeys: boolean\n}) {\n  const { key, optional, repeat } = parseMatchedParameter(segment)\n\n  // replace any non-word characters since they can break\n  // the named regex\n  let cleanedKey = key.replace(/\\W/g, '')\n\n  if (keyPrefix) {\n    cleanedKey = `${keyPrefix}${cleanedKey}`\n  }\n  let invalidKey = false\n\n  // check if the key is still invalid and fallback to using a known\n  // safe key\n  if (cleanedKey.length === 0 || cleanedKey.length > 30) {\n    invalidKey = true\n  }\n  if (!isNaN(parseInt(cleanedKey.slice(0, 1)))) {\n    invalidKey = true\n  }\n\n  if (invalidKey) {\n    cleanedKey = getSafeRouteKey()\n  }\n\n  const duplicateKey = cleanedKey in routeKeys\n\n  if (keyPrefix) {\n    routeKeys[cleanedKey] = `${keyPrefix}${key}`\n  } else {\n    routeKeys[cleanedKey] = key\n  }\n\n  // if the segment has an interception marker, make sure that's part of the regex pattern\n  // this is to ensure that the route with the interception marker doesn't incorrectly match\n  // the non-intercepted route (ie /app/(.)[username] should not match /app/[username])\n  const interceptionPrefix = interceptionMarker\n    ? escapeStringRegexp(interceptionMarker)\n    : ''\n\n  let pattern: string\n  if (duplicateKey && backreferenceDuplicateKeys) {\n    // Use a backreference to the key to ensure that the key is the same value\n    // in each of the placeholders.\n    pattern = `\\\\k<${cleanedKey}>`\n  } else if (repeat) {\n    pattern = `(?<${cleanedKey}>.+?)`\n  } else {\n    pattern = `(?<${cleanedKey}>[^/]+?)`\n  }\n\n  return optional\n    ? `(?:/${interceptionPrefix}${pattern})?`\n    : `/${interceptionPrefix}${pattern}`\n}\n\nfunction getNamedParametrizedRoute(\n  route: string,\n  prefixRouteKeys: boolean,\n  includeSuffix: boolean,\n  includePrefix: boolean,\n  backreferenceDuplicateKeys: boolean\n) {\n  const getSafeRouteKey = buildGetSafeRouteKey()\n  const routeKeys: { [named: string]: string } = {}\n\n  const segments: string[] = []\n  for (const segment of removeTrailingSlash(route).slice(1).split('/')) {\n    const hasInterceptionMarker = INTERCEPTION_ROUTE_MARKERS.some((m) =>\n      segment.startsWith(m)\n    )\n\n    const paramMatches = segment.match(PARAMETER_PATTERN) // Check for parameters\n\n    if (hasInterceptionMarker && paramMatches && paramMatches[2]) {\n      // If there's an interception marker, add it to the segments.\n      segments.push(\n        getSafeKeyFromSegment({\n          getSafeRouteKey,\n          interceptionMarker: paramMatches[1],\n          segment: paramMatches[2],\n          routeKeys,\n          keyPrefix: prefixRouteKeys\n            ? NEXT_INTERCEPTION_MARKER_PREFIX\n            : undefined,\n          backreferenceDuplicateKeys,\n        })\n      )\n    } else if (paramMatches && paramMatches[2]) {\n      // If there's a prefix, add it to the segments if it's enabled.\n      if (includePrefix && paramMatches[1]) {\n        segments.push(`/${escapeStringRegexp(paramMatches[1])}`)\n      }\n\n      let s = getSafeKeyFromSegment({\n        getSafeRouteKey,\n        segment: paramMatches[2],\n        routeKeys,\n        keyPrefix: prefixRouteKeys ? NEXT_QUERY_PARAM_PREFIX : undefined,\n        backreferenceDuplicateKeys,\n      })\n\n      // Remove the leading slash if includePrefix already added it.\n      if (includePrefix && paramMatches[1]) {\n        s = s.substring(1)\n      }\n\n      segments.push(s)\n    } else {\n      segments.push(`/${escapeStringRegexp(segment)}`)\n    }\n\n    // If there's a suffix, add it to the segments if it's enabled.\n    if (includeSuffix && paramMatches && paramMatches[3]) {\n      segments.push(escapeStringRegexp(paramMatches[3]))\n    }\n  }\n\n  return {\n    namedParameterizedRoute: segments.join(''),\n    routeKeys,\n  }\n}\n\n/**\n * This function extends `getRouteRegex` generating also a named regexp where\n * each group is named along with a routeKeys object that indexes the assigned\n * named group with its corresponding key. When the routeKeys need to be\n * prefixed to uniquely identify internally the \"prefixRouteKey\" arg should\n * be \"true\" currently this is only the case when creating the routes-manifest\n * during the build\n */\nexport function getNamedRouteRegex(\n  normalizedRoute: string,\n  options: GetNamedRouteRegexOptions\n) {\n  const result = getNamedParametrizedRoute(\n    normalizedRoute,\n    options.prefixRouteKeys,\n    options.includeSuffix ?? false,\n    options.includePrefix ?? false,\n    options.backreferenceDuplicateKeys ?? false\n  )\n\n  let namedRegex = result.namedParameterizedRoute\n  if (!options.excludeOptionalTrailingSlash) {\n    namedRegex += '(?:/)?'\n  }\n\n  return {\n    ...getRouteRegex(normalizedRoute, options),\n    namedRegex: `^${namedRegex}$`,\n    routeKeys: result.routeKeys,\n  }\n}\n\n/**\n * Generates a named regexp.\n * This is intended to be using for build time only.\n */\nexport function getNamedMiddlewareRegex(\n  normalizedRoute: string,\n  options: {\n    catchAll?: boolean\n  }\n) {\n  const { parameterizedRoute } = getParametrizedRoute(\n    normalizedRoute,\n    false,\n    false\n  )\n  const { catchAll = true } = options\n  if (parameterizedRoute === '/') {\n    let catchAllRegex = catchAll ? '.*' : ''\n    return {\n      namedRegex: `^/${catchAllRegex}$`,\n    }\n  }\n\n  const { namedParameterizedRoute } = getNamedParametrizedRoute(\n    normalizedRoute,\n    false,\n    false,\n    false,\n    false\n  )\n  let catchAllGroupedRegex = catchAll ? '(?:(/.*)?)' : ''\n  return {\n    namedRegex: `^${namedParameterizedRoute}${catchAllGroupedRegex}$`,\n  }\n}\n"], "names": ["NEXT_INTERCEPTION_MARKER_PREFIX", "NEXT_QUERY_PARAM_PREFIX", "INTERCEPTION_ROUTE_MARKERS", "escapeStringRegexp", "removeTrailingSlash", "PARAMETER_PATTERN", "parseParameter", "param", "match", "parseMatchedParameter", "optional", "startsWith", "endsWith", "slice", "repeat", "key", "getParametrizedRoute", "route", "includeSuffix", "includePrefix", "groups", "groupIndex", "segments", "segment", "split", "markerMatch", "find", "m", "paramMatch<PERSON>", "pos", "push", "s", "substring", "parameterizedRoute", "join", "getRouteRegex", "normalizedRoute", "excludeOptionalTrailingSlash", "re", "RegExp", "buildGetSafeRouteKey", "i", "routeKey", "j", "String", "fromCharCode", "Math", "floor", "getSafeKeyFromSegment", "<PERSON><PERSON><PERSON><PERSON>", "getSafeRouteKey", "routeKeys", "keyPrefix", "backreferenceDuplicateKeys", "<PERSON><PERSON><PERSON>", "replace", "<PERSON><PERSON><PERSON>", "length", "isNaN", "parseInt", "duplicate<PERSON>ey", "interceptionPrefix", "pattern", "getNamedParametrizedRoute", "prefixRouteKeys", "hasInterceptionMarker", "some", "undefined", "namedParameterizedRoute", "getNamedRouteRegex", "options", "result", "namedRegex", "getNamedMiddlewareRegex", "catchAll", "catchAllRegex", "catchAllGroupedRegex"], "mappings": ";;;;;;AAAA,SACEA,+BAA+B,EAC/BC,uBAAuB,QAClB,4BAA2B;AAClC,SAASC,0BAA0B,QAAQ,wBAAuB;AAClE,SAASC,kBAAkB,QAAQ,sBAAqB;AACxD,SAASC,mBAAmB,QAAQ,0BAAyB;;;;;AAyE7D;;;;;;;;CAQC,GACD,MAAMC,oBAAoB;AAcnB,SAASC,eAAeC,KAAa;IAC1C,MAAMC,QAAQD,MAAMC,KAAK,CAACH;IAE1B,IAAI,CAACG,OAAO;QACV,OAAOC,sBAAsBF;IAC/B;IAEA,OAAOE,sBAAsBD,KAAK,CAAC,EAAE;AACvC;AAEA;;;;;;;;;;CAUC,GACD,SAASC,sBAAsBF,KAAa;IAC1C,MAAMG,WAAWH,MAAMI,UAAU,CAAC,QAAQJ,MAAMK,QAAQ,CAAC;IACzD,IAAIF,UAAU;QACZH,QAAQA,MAAMM,KAAK,CAAC,GAAG,CAAC;IAC1B;IACA,MAAMC,SAASP,MAAMI,UAAU,CAAC;IAChC,IAAIG,QAAQ;QACVP,QAAQA,MAAMM,KAAK,CAAC;IACtB;IACA,OAAO;QAAEE,KAAKR;QAAOO;QAAQJ;IAAS;AACxC;AAEA,SAASM,qBACPC,KAAa,EACbC,aAAsB,EACtBC,aAAsB;IAEtB,MAAMC,SAAyC,CAAC;IAChD,IAAIC,aAAa;IAEjB,MAAMC,WAAqB,EAAE;IAC7B,KAAK,MAAMC,sNAAWnB,sBAAAA,EAAoBa,OAAOJ,KAAK,CAAC,GAAGW,KAAK,CAAC,KAAM;QACpE,MAAMC,gNAAcvB,6BAAAA,CAA2BwB,IAAI,CAAC,CAACC,IACnDJ,QAAQZ,UAAU,CAACgB;QAErB,MAAMC,eAAeL,QAAQf,KAAK,CAACH,mBAAmB,uBAAuB;;QAE7E,IAAIoB,eAAeG,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAClD,MAAM,EAAEb,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGL,sBAAsBmB,YAAY,CAAC,EAAE;YACvER,MAAM,CAACL,IAAI,GAAG;gBAAEc,KAAKR;gBAAcP;gBAAQJ;YAAS;YACpDY,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmBsB,eAAa;QACpD,OAAO,IAAIG,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAEb,GAAG,EAAED,MAAM,EAAEJ,QAAQ,EAAE,GAAGD,sBAAsBmB,YAAY,CAAC,EAAE;YACvER,MAAM,CAACL,IAAI,GAAG;gBAAEc,KAAKR;gBAAcP;gBAAQJ;YAAS;YAEpD,IAAIS,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCN,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmByB,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIG,IAAIjB,SAAUJ,WAAW,gBAAgB,WAAY;YAEzD,8DAA8D;YAC9D,IAAIS,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCG,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEAV,SAASQ,IAAI,CAACC;QAChB,OAAO;YACLT,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmBoB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBU,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDN,SAASQ,IAAI,EAAC3B,iMAAAA,EAAmByB,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLK,oBAAoBX,SAASY,IAAI,CAAC;QAClCd;IACF;AACF;AAOO,SAASe,cACdC,eAAuB,EACvB,KAAA;IAAA,IAAA,EACElB,gBAAgB,KAAK,EACrBC,gBAAgB,KAAK,EACrBkB,+BAA+B,KAAK,EACf,GAJvB,UAAA,KAAA,IAI0B,CAAC,IAJ3B;IAMA,MAAM,EAAEJ,kBAAkB,EAAEb,MAAM,EAAE,GAAGJ,qBACrCoB,iBACAlB,eACAC;IAGF,IAAImB,KAAKL;IACT,IAAI,CAACI,8BAA8B;QACjCC,MAAM;IACR;IAEA,OAAO;QACLA,IAAI,IAAIC,OAAQ,MAAGD,KAAG;QACtBlB,QAAQA;IACV;AACF;AAEA;;;CAGC,GACD,SAASoB;IACP,IAAIC,IAAI;IAER,OAAO;QACL,IAAIC,WAAW;QACf,IAAIC,IAAI,EAAEF;QACV,MAAOE,IAAI,EAAG;YACZD,YAAYE,OAAOC,YAAY,CAAC,KAAOF,CAAAA,IAAI,CAAA,IAAK;YAChDA,IAAIG,KAAKC,KAAK,CAAEJ,CAAAA,IAAI,CAAA,IAAK;QAC3B;QACA,OAAOD;IACT;AACF;AAEA,SAASM,sBAAsB,KAc9B;IAd8B,IAAA,EAC7BC,kBAAkB,EAClBC,eAAe,EACf3B,OAAO,EACP4B,SAAS,EACTC,SAAS,EACTC,0BAA0B,EAQ3B,GAd8B;IAe7B,MAAM,EAAEtC,GAAG,EAAEL,QAAQ,EAAEI,MAAM,EAAE,GAAGL,sBAAsBc;IAExD,uDAAuD;IACvD,kBAAkB;IAClB,IAAI+B,aAAavC,IAAIwC,OAAO,CAAC,OAAO;IAEpC,IAAIH,WAAW;QACbE,aAAc,KAAEF,YAAYE;IAC9B;IACA,IAAIE,aAAa;IAEjB,kEAAkE;IAClE,WAAW;IACX,IAAIF,WAAWG,MAAM,KAAK,KAAKH,WAAWG,MAAM,GAAG,IAAI;QACrDD,aAAa;IACf;IACA,IAAI,CAACE,MAAMC,SAASL,WAAWzC,KAAK,CAAC,GAAG,MAAM;QAC5C2C,aAAa;IACf;IAEA,IAAIA,YAAY;QACdF,aAAaJ;IACf;IAEA,MAAMU,eAAeN,cAAcH;IAEnC,IAAIC,WAAW;QACbD,SAAS,CAACG,WAAW,GAAI,KAAEF,YAAYrC;IACzC,OAAO;QACLoC,SAAS,CAACG,WAAW,GAAGvC;IAC1B;IAEA,wFAAwF;IACxF,0FAA0F;IAC1F,qFAAqF;IACrF,MAAM8C,qBAAqBZ,kMACvB9C,qBAAAA,EAAmB8C,sBACnB;IAEJ,IAAIa;IACJ,IAAIF,gBAAgBP,4BAA4B;QAC9C,0EAA0E;QAC1E,+BAA+B;QAC/BS,UAAW,SAAMR,aAAW;IAC9B,OAAO,IAAIxC,QAAQ;QACjBgD,UAAW,QAAKR,aAAW;IAC7B,OAAO;QACLQ,UAAW,QAAKR,aAAW;IAC7B;IAEA,OAAO5C,WACF,SAAMmD,qBAAqBC,UAAQ,OACnC,MAAGD,qBAAqBC;AAC/B;AAEA,SAASC,0BACP9C,KAAa,EACb+C,eAAwB,EACxB9C,aAAsB,EACtBC,aAAsB,EACtBkC,0BAAmC;IAEnC,MAAMH,kBAAkBV;IACxB,MAAMW,YAAyC,CAAC;IAEhD,MAAM7B,WAAqB,EAAE;IAC7B,KAAK,MAAMC,sNAAWnB,sBAAAA,EAAoBa,OAAOJ,KAAK,CAAC,GAAGW,KAAK,CAAC,KAAM;QACpE,MAAMyC,wBAAwB/D,+NAAAA,CAA2BgE,IAAI,CAAC,CAACvC,IAC7DJ,QAAQZ,UAAU,CAACgB;QAGrB,MAAMC,eAAeL,QAAQf,KAAK,CAACH,mBAAmB,uBAAuB;;QAE7E,IAAI4D,yBAAyBrC,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC5D,6DAA6D;YAC7DN,SAASQ,IAAI,CACXkB,sBAAsB;gBACpBE;gBACAD,oBAAoBrB,YAAY,CAAC,EAAE;gBACnCL,SAASK,YAAY,CAAC,EAAE;gBACxBuB;gBACAC,WAAWY,0KACPhE,kCAAAA,GACAmE;gBACJd;YACF;QAEJ,OAAO,IAAIzB,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YAC1C,+DAA+D;YAC/D,IAAIT,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCN,SAASQ,IAAI,CAAE,mLAAG3B,qBAAAA,EAAmByB,YAAY,CAAC,EAAE;YACtD;YAEA,IAAIG,IAAIiB,sBAAsB;gBAC5BE;gBACA3B,SAASK,YAAY,CAAC,EAAE;gBACxBuB;gBACAC,WAAWY,0KAAkB/D,0BAAAA,GAA0BkE;gBACvDd;YACF;YAEA,8DAA8D;YAC9D,IAAIlC,iBAAiBS,YAAY,CAAC,EAAE,EAAE;gBACpCG,IAAIA,EAAEC,SAAS,CAAC;YAClB;YAEAV,SAASQ,IAAI,CAACC;QAChB,OAAO;YACLT,SAASQ,IAAI,CAAE,MAAG3B,kMAAAA,EAAmBoB;QACvC;QAEA,+DAA+D;QAC/D,IAAIL,iBAAiBU,gBAAgBA,YAAY,CAAC,EAAE,EAAE;YACpDN,SAASQ,IAAI,KAAC3B,8LAAAA,EAAmByB,YAAY,CAAC,EAAE;QAClD;IACF;IAEA,OAAO;QACLwC,yBAAyB9C,SAASY,IAAI,CAAC;QACvCiB;IACF;AACF;AAUO,SAASkB,mBACdjC,eAAuB,EACvBkC,OAAkC;QAKhCA,wBACAA,wBACAA;IALF,MAAMC,SAASR,0BACb3B,iBACAkC,QAAQN,eAAe,EACvBM,CAAAA,yBAAAA,QAAQpD,aAAa,KAAA,OAArBoD,yBAAyB,OACzBA,CAAAA,yBAAAA,QAAQnD,aAAa,KAAA,OAArBmD,yBAAyB,OACzBA,CAAAA,sCAAAA,QAAQjB,0BAA0B,KAAA,OAAlCiB,sCAAsC;IAGxC,IAAIE,aAAaD,OAAOH,uBAAuB;IAC/C,IAAI,CAACE,QAAQjC,4BAA4B,EAAE;QACzCmC,cAAc;IAChB;IAEA,OAAO;QACL,GAAGrC,cAAcC,iBAAiBkC,QAAQ;QAC1CE,YAAa,MAAGA,aAAW;QAC3BrB,WAAWoB,OAAOpB,SAAS;IAC7B;AACF;AAMO,SAASsB,wBACdrC,eAAuB,EACvBkC,OAEC;IAED,MAAM,EAAErC,kBAAkB,EAAE,GAAGjB,qBAC7BoB,iBACA,OACA;IAEF,MAAM,EAAEsC,WAAW,IAAI,EAAE,GAAGJ;IAC5B,IAAIrC,uBAAuB,KAAK;QAC9B,IAAI0C,gBAAgBD,WAAW,OAAO;QACtC,OAAO;YACLF,YAAa,OAAIG,gBAAc;QACjC;IACF;IAEA,MAAM,EAAEP,uBAAuB,EAAE,GAAGL,0BAClC3B,iBACA,OACA,OACA,OACA;IAEF,IAAIwC,uBAAuBF,WAAW,eAAe;IACrD,OAAO;QACLF,YAAa,MAAGJ,0BAA0BQ,uBAAqB;IACjE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/request/fallback-params.ts"], "sourcesContent": ["import { getRouteMatcher } from '../../shared/lib/router/utils/route-matcher'\nimport { getRouteRegex } from '../../shared/lib/router/utils/route-regex'\n\nexport type FallbackRouteParams = ReadonlyMap<string, string>\n\nfunction getParamKeys(page: string) {\n  const pattern = getRouteRegex(page)\n  const matcher = getRouteMatcher(pattern)\n\n  // Get the default list of allowed params.\n  return Object.keys(matcher(page))\n}\n\nexport function getFallbackRouteParams(\n  pageOrKeys: string | readonly string[]\n): FallbackRouteParams | null {\n  let keys: readonly string[]\n  if (typeof pageOrKeys === 'string') {\n    keys = getParamKeys(pageOrKeys)\n  } else {\n    keys = pageOrKeys\n  }\n\n  // If there are no keys, we can return early.\n  if (keys.length === 0) return null\n\n  const params = new Map<string, string>()\n\n  // As we're creating unique keys for each of the dynamic route params, we only\n  // need to generate a unique ID once per request because each of the keys will\n  // be also be unique.\n  const uniqueID = Math.random().toString(16).slice(2)\n\n  for (const key of keys) {\n    params.set(key, `%%drp:${key}:${uniqueID}%%`)\n  }\n\n  return params\n}\n"], "names": ["getRouteMatcher", "getRouteRegex", "get<PERSON>ara<PERSON><PERSON><PERSON><PERSON>", "page", "pattern", "matcher", "Object", "keys", "getFallbackRouteParams", "page<PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "params", "Map", "uniqueID", "Math", "random", "toString", "slice", "key", "set"], "mappings": ";;;AAAA,SAASA,eAAe,QAAQ,8CAA6C;AAC7E,SAASC,aAAa,QAAQ,4CAA2C;;;AAIzE,SAASC,aAAaC,IAAY;IAChC,MAAMC,wMAAUH,gBAAAA,EAAcE;IAC9B,MAAME,0MAAUL,kBAAAA,EAAgBI;IAEhC,0CAA0C;IAC1C,OAAOE,OAAOC,IAAI,CAACF,QAAQF;AAC7B;AAEO,SAASK,uBACdC,UAAsC;IAEtC,IAAIF;IACJ,IAAI,OAAOE,eAAe,UAAU;QAClCF,OAAOL,aAAaO;IACtB,OAAO;QACLF,OAAOE;IACT;IAEA,6CAA6C;IAC7C,IAAIF,KAAKG,MAAM,KAAK,GAAG,OAAO;IAE9B,MAAMC,SAAS,IAAIC;IAEnB,8EAA8E;IAC9E,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMC,WAAWC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC;IAElD,KAAK,MAAMC,OAAOX,KAAM;QACtBI,OAAOQ,GAAG,CAACD,KAAK,CAAC,MAAM,EAAEA,IAAI,CAAC,EAAEL,SAAS,EAAE,CAAC;IAC9C;IAEA,OAAOF;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3396, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/app-render/encryption-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport type {\n  ClientReferenceManifest,\n  ClientReferenceManifestForRsc,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { workAsyncStorage } from './work-async-storage.external'\n\nlet __next_loaded_action_key: CryptoKey\n\nexport function arrayBufferToString(\n  buffer: ArrayBuffer | Uint8Array<ArrayBufferLike>\n) {\n  const bytes = new Uint8Array(buffer)\n  const len = bytes.byteLength\n\n  // @anonrig: V8 has a limit of 65535 arguments in a function.\n  // For len < 65535, this is faster.\n  // https://github.com/vercel/next.js/pull/56377#pullrequestreview-1656181623\n  if (len < 65535) {\n    return String.fromCharCode.apply(null, bytes as unknown as number[])\n  }\n\n  let binary = ''\n  for (let i = 0; i < len; i++) {\n    binary += String.fromCharCode(bytes[i])\n  }\n  return binary\n}\n\nexport function stringToUint8Array(binary: string) {\n  const len = binary.length\n  const arr = new Uint8Array(len)\n\n  for (let i = 0; i < len; i++) {\n    arr[i] = binary.charCodeAt(i)\n  }\n\n  return arr\n}\n\nexport function encrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.encrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\nexport function decrypt(key: CryptoKey, iv: Uint8Array, data: Uint8Array) {\n  return crypto.subtle.decrypt(\n    {\n      name: 'AES-GCM',\n      iv,\n    },\n    key,\n    data\n  )\n}\n\n// This is a global singleton that is used to encode/decode the action bound args from\n// the closure. This can't be using a AsyncLocalStorage as it might happen on the module\n// level. Since the client reference manifest won't be mutated, let's use a global singleton\n// to keep it.\nconst SERVER_ACTION_MANIFESTS_SINGLETON = Symbol.for(\n  'next.server.action-manifests'\n)\n\nexport function setReferenceManifestsSingleton({\n  page,\n  clientReferenceManifest,\n  serverActionsManifest,\n  serverModuleMap,\n}: {\n  page: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  serverActionsManifest: DeepReadonly<ActionManifest>\n  serverModuleMap: {\n    [id: string]: {\n      id: string\n      chunks: string[]\n      name: string\n    }\n  }\n}) {\n  // @ts-expect-error\n  const clientReferenceManifestsPerPage = globalThis[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ]?.clientReferenceManifestsPerPage as\n    | undefined\n    | DeepReadonly<Record<string, ClientReferenceManifest>>\n\n  // @ts-expect-error\n  globalThis[SERVER_ACTION_MANIFESTS_SINGLETON] = {\n    clientReferenceManifestsPerPage: {\n      ...clientReferenceManifestsPerPage,\n      [normalizeAppPath(page)]: clientReferenceManifest,\n    },\n    serverActionsManifest,\n    serverModuleMap,\n  }\n}\n\nexport function getServerModuleMap() {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverModuleMap: {\n      [id: string]: {\n        id: string\n        chunks: string[]\n        name: string\n      }\n    }\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  return serverActionsManifestSingleton.serverModuleMap\n}\n\nexport function getClientReferenceManifestForRsc(): DeepReadonly<ClientReferenceManifestForRsc> {\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    clientReferenceManifestsPerPage: DeepReadonly<\n      Record<string, ClientReferenceManifest>\n    >\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const { clientReferenceManifestsPerPage } = serverActionsManifestSingleton\n  const workStore = workAsyncStorage.getStore()\n\n  if (!workStore) {\n    // If there's no work store defined, we can assume that a client reference\n    // manifest is needed during module evaluation, e.g. to create a server\n    // action using a higher-order function. This might also use client\n    // components which need to be serialized by Flight, and therefore client\n    // references need to be resolvable. To make this work, we're returning a\n    // merged manifest across all pages. This is fine as long as the module IDs\n    // are not page specific, which they are not for Webpack. TODO: Fix this in\n    // Turbopack.\n    return mergeClientReferenceManifests(clientReferenceManifestsPerPage)\n  }\n\n  const clientReferenceManifest =\n    clientReferenceManifestsPerPage[workStore.route]\n\n  if (!clientReferenceManifest) {\n    throw new InvariantError(\n      `Missing Client Reference Manifest for ${workStore.route}.`\n    )\n  }\n\n  return clientReferenceManifest\n}\n\nexport async function getActionEncryptionKey() {\n  if (__next_loaded_action_key) {\n    return __next_loaded_action_key\n  }\n\n  const serverActionsManifestSingleton = (globalThis as any)[\n    SERVER_ACTION_MANIFESTS_SINGLETON\n  ] as {\n    serverActionsManifest: DeepReadonly<ActionManifest>\n  }\n\n  if (!serverActionsManifestSingleton) {\n    throw new InvariantError('Missing manifest for Server Actions.')\n  }\n\n  const rawKey =\n    process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY ||\n    serverActionsManifestSingleton.serverActionsManifest.encryptionKey\n\n  if (rawKey === undefined) {\n    throw new InvariantError('Missing encryption key for Server Actions')\n  }\n\n  __next_loaded_action_key = await crypto.subtle.importKey(\n    'raw',\n    stringToUint8Array(atob(rawKey)),\n    'AES-GCM',\n    true,\n    ['encrypt', 'decrypt']\n  )\n\n  return __next_loaded_action_key\n}\n\nfunction mergeClientReferenceManifests(\n  clientReferenceManifestsPerPage: DeepReadonly<\n    Record<string, ClientReferenceManifest>\n  >\n): ClientReferenceManifestForRsc {\n  const clientReferenceManifests = Object.values(\n    clientReferenceManifestsPerPage as Record<string, ClientReferenceManifest>\n  )\n\n  const mergedClientReferenceManifest: ClientReferenceManifestForRsc = {\n    clientModules: {},\n    edgeRscModuleMapping: {},\n    rscModuleMapping: {},\n  }\n\n  for (const clientReferenceManifest of clientReferenceManifests) {\n    mergedClientReferenceManifest.clientModules = {\n      ...mergedClientReferenceManifest.clientModules,\n      ...clientReferenceManifest.clientModules,\n    }\n    mergedClientReferenceManifest.edgeRscModuleMapping = {\n      ...mergedClientReferenceManifest.edgeRscModuleMapping,\n      ...clientReferenceManifest.edgeRscModuleMapping,\n    }\n    mergedClientReferenceManifest.rscModuleMapping = {\n      ...mergedClientReferenceManifest.rscModuleMapping,\n      ...clientReferenceManifest.rscModuleMapping,\n    }\n  }\n\n  return mergedClientReferenceManifest\n}\n"], "names": ["InvariantError", "normalizeAppPath", "workAsyncStorage", "__next_loaded_action_key", "arrayBufferToString", "buffer", "bytes", "Uint8Array", "len", "byteLength", "String", "fromCharCode", "apply", "binary", "i", "stringToUint8Array", "length", "arr", "charCodeAt", "encrypt", "key", "iv", "data", "crypto", "subtle", "name", "decrypt", "SERVER_ACTION_MANIFESTS_SINGLETON", "Symbol", "for", "setReferenceManifestsSingleton", "page", "clientReferenceManifest", "serverActionsManifest", "serverModuleMap", "globalThis", "clientReferenceManifestsPerPage", "getServerModuleMap", "serverActionsManifestSingleton", "getClientReferenceManifestForRsc", "workStore", "getStore", "mergeClientReferenceManifests", "route", "getActionEncryptionKey", "<PERSON><PERSON><PERSON>", "process", "env", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "<PERSON><PERSON><PERSON>", "undefined", "importKey", "atob", "clientReferenceManifests", "Object", "values", "mergedClientReferenceManifest", "clientModules", "edgeRscModuleMapping", "rscModuleMapping"], "mappings": ";;;;;;;;;;AAMA,SAASA,cAAc,QAAQ,mCAAkC;AACjE,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,gBAAgB,QAAQ,gCAA+B;;;;AAEhE,IAAIC;AAEG,SAASC,oBACdC,MAAiD;IAEjD,MAAMC,QAAQ,IAAIC,WAAWF;IAC7B,MAAMG,MAAMF,MAAMG,UAAU;IAE5B,6DAA6D;IAC7D,mCAAmC;IACnC,4EAA4E;IAC5E,IAAID,MAAM,OAAO;QACf,OAAOE,OAAOC,YAAY,CAACC,KAAK,CAAC,MAAMN;IACzC;IAEA,IAAIO,SAAS;IACb,IAAK,IAAIC,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BD,UAAUH,OAAOC,YAAY,CAACL,KAAK,CAACQ,EAAE;IACxC;IACA,OAAOD;AACT;AAEO,SAASE,mBAAmBF,MAAc;IAC/C,MAAML,MAAMK,OAAOG,MAAM;IACzB,MAAMC,MAAM,IAAIV,WAAWC;IAE3B,IAAK,IAAIM,IAAI,GAAGA,IAAIN,KAAKM,IAAK;QAC5BG,GAAG,CAACH,EAAE,GAAGD,OAAOK,UAAU,CAACJ;IAC7B;IAEA,OAAOG;AACT;AAEO,SAASE,QAAQC,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACL,OAAO,CAC1B;QACEM,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEO,SAASI,QAAQN,GAAc,EAAEC,EAAc,EAAEC,IAAgB;IACtE,OAAOC,OAAOC,MAAM,CAACE,OAAO,CAC1B;QACED,MAAM;QACNJ;IACF,GACAD,KACAE;AAEJ;AAEA,sFAAsF;AACtF,wFAAwF;AACxF,4FAA4F;AAC5F,cAAc;AACd,MAAMK,oCAAoCC,OAAOC,GAAG,CAClD;AAGK,SAASC,+BAA+B,EAC7CC,IAAI,EACJC,uBAAuB,EACvBC,qBAAqB,EACrBC,eAAe,EAYhB;QAEyCC;IADxC,mBAAmB;IACnB,MAAMC,kCAAAA,CAAkCD,gDAAAA,UAAU,CAChDR,kCACD,KAAA,OAAA,KAAA,IAFuCQ,8CAErCC,+BAA+B;IAIlC,mBAAmB;IACnBD,UAAU,CAACR,kCAAkC,GAAG;QAC9CS,iCAAiC;YAC/B,GAAGA,+BAA+B;YAClC,6LAACnC,mBAAAA,EAAiB8B,MAAM,EAAEC;QAC5B;QACAC;QACAC;IACF;AACF;AAEO,SAASG;IACd,MAAMC,iCAAkCH,UAAkB,CACxDR,kCACD;IAUD,IAAI,CAACW,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,+KAAItC,iBAAAA,CAAe,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,OAAOsC,+BAA+BJ,eAAe;AACvD;AAEO,SAASK;IACd,MAAMD,iCAAkCH,UAAkB,CACxDR,kCACD;IAMD,IAAI,CAACW,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,+KAAItC,iBAAAA,CAAe,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAM,EAAEoC,+BAA+B,EAAE,GAAGE;IAC5C,MAAME,gRAAYtC,mBAAAA,CAAiBuC,QAAQ;IAE3C,IAAI,CAACD,WAAW;QACd,0EAA0E;QAC1E,uEAAuE;QACvE,mEAAmE;QACnE,yEAAyE;QACzE,yEAAyE;QACzE,2EAA2E;QAC3E,2EAA2E;QAC3E,aAAa;QACb,OAAOE,8BAA8BN;IACvC;IAEA,MAAMJ,0BACJI,+BAA+B,CAACI,UAAUG,KAAK,CAAC;IAElD,IAAI,CAACX,yBAAyB;QAC5B,MAAM,OAAA,cAEL,CAFK,+KAAIhC,iBAAAA,CACR,CAAC,sCAAsC,EAAEwC,UAAUG,KAAK,CAAC,CAAC,CAAC,GADvD,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,OAAOX;AACT;AAEO,eAAeY;IACpB,IAAIzC,0BAA0B;QAC5B,OAAOA;IACT;IAEA,MAAMmC,iCAAkCH,UAAkB,CACxDR,kCACD;IAID,IAAI,CAACW,gCAAgC;QACnC,MAAM,OAAA,cAA0D,CAA1D,+KAAItC,iBAAAA,CAAe,yCAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAAyD;IACjE;IAEA,MAAM6C,SACJC,QAAQC,GAAG,CAACC,kCAAkC,IAC9CV,+BAA+BL,qBAAqB,CAACgB,aAAa;IAEpE,IAAIJ,WAAWK,WAAW;QACxB,MAAM,OAAA,cAA+D,CAA/D,+KAAIlD,iBAAAA,CAAe,8CAAnB,qBAAA;mBAAA;wBAAA;0BAAA;QAA8D;IACtE;IAEAG,2BAA2B,MAAMoB,OAAOC,MAAM,CAAC2B,SAAS,CACtD,OACApC,mBAAmBqC,KAAKP,UACxB,WACA,MACA;QAAC;QAAW;KAAU;IAGxB,OAAO1C;AACT;AAEA,SAASuC,8BACPN,+BAEC;IAED,MAAMiB,2BAA2BC,OAAOC,MAAM,CAC5CnB;IAGF,MAAMoB,gCAA+D;QACnEC,eAAe,CAAC;QAChBC,sBAAsB,CAAC;QACvBC,kBAAkB,CAAC;IACrB;IAEA,KAAK,MAAM3B,2BAA2BqB,yBAA0B;QAC9DG,8BAA8BC,aAAa,GAAG;YAC5C,GAAGD,8BAA8BC,aAAa;YAC9C,GAAGzB,wBAAwByB,aAAa;QAC1C;QACAD,8BAA8BE,oBAAoB,GAAG;YACnD,GAAGF,8BAA8BE,oBAAoB;YACrD,GAAG1B,wBAAwB0B,oBAAoB;QACjD;QACAF,8BAA8BG,gBAAgB,GAAG;YAC/C,GAAGH,8BAA8BG,gBAAgB;YACjD,GAAG3B,wBAAwB2B,gBAAgB;QAC7C;IACF;IAEA,OAAOH;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3563, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/router/utils/html-bots.ts"], "sourcesContent": ["// This regex contains the bots that we need to do a blocking render for and can't safely stream the response\n// due to how they parse the DOM. For example, they might explicitly check for metadata in the `head` tag, so we can't stream metadata tags after the `head` was sent.\nexport const HTML_LIMITED_BOT_UA_RE =\n  /Mediapartners-Google|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i\n"], "names": ["HTML_LIMITED_BOT_UA_RE"], "mappings": "AAAA,6GAA6G;AAC7G,sKAAsK;;;;AAC/J,MAAMA,yBACX,gSAA+R", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3573, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/shared/lib/router/utils/is-bot.ts"], "sourcesContent": ["import { HTML_LIMITED_BOT_UA_RE } from './html-bots'\n\n// Bot crawler that will spin up a headless browser and execute JS.\n// By default, only googlebots are considered as DOM bots. Blow is where the regex is computed from:\n// x-ref: https://developers.google.com/search/docs/crawling-indexing/google-common-crawlers\nconst HEADLESS_BROWSER_BOT_UA_RE = /google/i\n\nexport const HTML_LIMITED_BOT_UA_RE_STRING = HTML_LIMITED_BOT_UA_RE.source\n\nexport { HTML_LIMITED_BOT_UA_RE }\n\nfunction isDomBotUA(userAgent: string) {\n  return HEADLESS_BROWSER_BOT_UA_RE.test(userAgent)\n}\n\nfunction isHtmlLimitedBotUA(userAgent: string) {\n  return HTML_LIMITED_BOT_UA_RE.test(userAgent)\n}\n\nexport function isBot(userAgent: string): boolean {\n  return isDomBotUA(userAgent) || isHtmlLimitedBotUA(userAgent)\n}\n\nexport function getBotType(userAgent: string): 'dom' | 'html' | undefined {\n  if (isDomBotUA(userAgent)) {\n    return 'dom'\n  }\n  if (isHtmlLimitedBotUA(userAgent)) {\n    return 'html'\n  }\n  return undefined\n}\n"], "names": ["HTML_LIMITED_BOT_UA_RE", "HEADLESS_BROWSER_BOT_UA_RE", "HTML_LIMITED_BOT_UA_RE_STRING", "source", "isDomBotUA", "userAgent", "test", "isHtmlLimitedBotUA", "isBot", "getBotType", "undefined"], "mappings": ";;;;;AAAA,SAASA,sBAAsB,QAAQ,cAAa;;AAEpD,mEAAmE;AACnE,oGAAoG;AACpG,4FAA4F;AAC5F,MAAMC,6BAA6B;AAE5B,MAAMC,wNAAgCF,yBAAAA,CAAuBG,MAAM,CAAA;;AAI1E,SAASC,WAAWC,SAAiB;IACnC,OAAOJ,2BAA2BK,IAAI,CAACD;AACzC;AAEA,SAASE,mBAAmBF,SAAiB;IAC3C,+LAAOL,yBAAAA,CAAuBM,IAAI,CAACD;AACrC;AAEO,SAASG,MAAMH,SAAiB;IACrC,OAAOD,WAAWC,cAAcE,mBAAmBF;AACrD;AAEO,SAASI,WAAWJ,SAAiB;IAC1C,IAAID,WAAWC,YAAY;QACzB,OAAO;IACT;IACA,IAAIE,mBAAmBF,YAAY;QACjC,OAAO;IACT;IACA,OAAOK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3615, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/lib/streaming-metadata.ts"], "sourcesContent": ["import {\n  getBotType,\n  HTML_LIMITED_BOT_UA_RE_STRING,\n} from '../../shared/lib/router/utils/is-bot'\nimport type { BaseNextRequest } from '../base-http'\n\nexport function shouldServeStreamingMetadata(\n  userAgent: string,\n  htmlLimitedBots: string | undefined\n): boolean {\n  const blockingMetadataUARegex = new RegExp(\n    htmlLimitedBots || HTML_LIMITED_BOT_UA_RE_STRING,\n    'i'\n  )\n  // Only block metadata for HTML-limited bots\n  if (userAgent && blockingMetadataUARegex.test(userAgent)) {\n    return false\n  }\n  return true\n}\n\n// When the request UA is a html-limited bot, we should do a dynamic render.\n// In this case, postpone state is not sent.\nexport function isHtmlBotRequest(req: {\n  headers: BaseNextRequest['headers']\n}): boolean {\n  const ua = req.headers['user-agent'] || ''\n  const botType = getBotType(ua)\n\n  return botType === 'html'\n}\n"], "names": ["getBotType", "HTML_LIMITED_BOT_UA_RE_STRING", "shouldServeStreamingMetadata", "userAgent", "htmlLimitedBots", "blockingMetadataUARegex", "RegExp", "test", "isHtmlBotRequest", "req", "ua", "headers", "botType"], "mappings": ";;;;AAAA,SACEA,UAAU,EACVC,6BAA6B,QACxB,uCAAsC;;;AAGtC,SAASC,6BACdC,SAAiB,EACjBC,eAAmC;IAEnC,MAAMC,0BAA0B,IAAIC,OAClCF,wNAAmBH,gCAAAA,EACnB;IAEF,4CAA4C;IAC5C,IAAIE,aAAaE,wBAAwBE,IAAI,CAACJ,YAAY;QACxD,OAAO;IACT;IACA,OAAO;AACT;AAIO,SAASK,iBAAiBC,GAEhC;IACC,MAAMC,KAAKD,IAAIE,OAAO,CAAC,aAAa,IAAI;IACxC,MAAMC,mNAAUZ,aAAAA,EAAWU;IAE3B,OAAOE,YAAY;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3639, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/app-render/action-utils.ts"], "sourcesContent": ["import type { ActionManifest } from '../../build/webpack/plugins/flight-client-entry-plugin'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { pathHasPrefix } from '../../shared/lib/router/utils/path-has-prefix'\nimport { removePathPrefix } from '../../shared/lib/router/utils/remove-path-prefix'\nimport { workAsyncStorage } from './work-async-storage.external'\n\n// This function creates a Flight-acceptable server module map proxy from our\n// Server Reference Manifest similar to our client module map.\n// This is because our manifest contains a lot of internal Next.js data that\n// are relevant to the runtime, workers, etc. that <PERSON>act doesn't need to know.\nexport function createServerModuleMap({\n  serverActionsManifest,\n}: {\n  serverActionsManifest: ActionManifest\n}) {\n  return new Proxy(\n    {},\n    {\n      get: (_, id: string) => {\n        const workers =\n          serverActionsManifest[\n            process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'\n          ]?.[id]?.workers\n\n        if (!workers) {\n          return undefined\n        }\n\n        const workStore = workAsyncStorage.getStore()\n\n        let workerEntry:\n          | { moduleId: string | number; async: boolean }\n          | undefined\n\n        if (workStore) {\n          workerEntry = workers[normalizeWorkerPageName(workStore.page)]\n        } else {\n          // If there's no work store defined, we can assume that a server\n          // module map is needed during module evaluation, e.g. to create a\n          // server action using a higher-order function. Therefore it should be\n          // safe to return any entry from the manifest that matches the action\n          // ID. They all refer to the same module ID, which must also exist in\n          // the current page bundle. TODO: This is currently not guaranteed in\n          // Turbopack, and needs to be fixed.\n          workerEntry = Object.values(workers).at(0)\n        }\n\n        if (!workerEntry) {\n          return undefined\n        }\n\n        const { moduleId, async } = workerEntry\n\n        return { id: moduleId, name: id, chunks: [], async }\n      },\n    }\n  )\n}\n\n/**\n * Checks if the requested action has a worker for the current page.\n * If not, it returns the first worker that has a handler for the action.\n */\nexport function selectWorkerForForwarding(\n  actionId: string,\n  pageName: string,\n  serverActionsManifest: ActionManifest\n) {\n  const workers =\n    serverActionsManifest[\n      process.env.NEXT_RUNTIME === 'edge' ? 'edge' : 'node'\n    ][actionId]?.workers\n  const workerName = normalizeWorkerPageName(pageName)\n\n  // no workers, nothing to forward to\n  if (!workers) return\n\n  // if there is a worker for this page, no need to forward it.\n  if (workers[workerName]) {\n    return\n  }\n\n  // otherwise, grab the first worker that has a handler for this action id\n  return denormalizeWorkerPageName(Object.keys(workers)[0])\n}\n\n/**\n * The flight entry loader keys actions by bundlePath.\n * bundlePath corresponds with the relative path (including 'app') to the page entrypoint.\n */\nfunction normalizeWorkerPageName(pageName: string) {\n  if (pathHasPrefix(pageName, 'app')) {\n    return pageName\n  }\n\n  return 'app' + pageName\n}\n\n/**\n * Converts a bundlePath (relative path to the entrypoint) to a routable page name\n */\nfunction denormalizeWorkerPageName(bundlePath: string) {\n  return normalizeAppPath(removePathPrefix(bundlePath, 'app'))\n}\n"], "names": ["normalizeAppPath", "pathHasPrefix", "removePathPrefix", "workAsyncStorage", "createServerModuleMap", "serverActionsManifest", "Proxy", "get", "_", "id", "workers", "process", "env", "NEXT_RUNTIME", "undefined", "workStore", "getStore", "workerEntry", "normalizeWorkerPageName", "page", "Object", "values", "at", "moduleId", "async", "name", "chunks", "selectWorkerForForwarding", "actionId", "pageName", "worker<PERSON>ame", "denormalizeWorkerPageName", "keys", "bundlePath"], "mappings": ";;;;AACA,SAASA,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,SAASC,gBAAgB,QAAQ,gCAA+B;;;;;AAMzD,SAASC,sBAAsB,EACpCC,qBAAqB,EAGtB;IACC,OAAO,IAAIC,MACT,CAAC,GACD;QACEC,KAAK,CAACC,GAAGC;gBAELJ,4BAAAA;YADF,MAAMK,UAAAA,CACJL,0BAAAA,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,0BAAS,OAChD,KAAA,OAAA,KAAA,IAAA,CAFDR,6BAAAA,uBAEG,CAACI,GAAG,KAAA,OAAA,KAAA,IAFPJ,2BAESK,OAAO;YAElB,IAAI,CAACA,SAAS;gBACZ,OAAOI;YACT;YAEA,MAAMC,gRAAYZ,mBAAAA,CAAiBa,QAAQ;YAE3C,IAAIC;YAIJ,IAAIF,WAAW;gBACbE,cAAcP,OAAO,CAACQ,wBAAwBH,UAAUI,IAAI,EAAE;YAChE,OAAO;gBACL,gEAAgE;gBAChE,kEAAkE;gBAClE,sEAAsE;gBACtE,qEAAqE;gBACrE,qEAAqE;gBACrE,qEAAqE;gBACrE,oCAAoC;gBACpCF,cAAcG,OAAOC,MAAM,CAACX,SAASY,EAAE,CAAC;YAC1C;YAEA,IAAI,CAACL,aAAa;gBAChB,OAAOH;YACT;YAEA,MAAM,EAAES,QAAQ,EAAEC,KAAK,EAAE,GAAGP;YAE5B,OAAO;gBAAER,IAAIc;gBAAUE,MAAMhB;gBAAIiB,QAAQ,EAAE;gBAAEF;YAAM;QACrD;IACF;AAEJ;AAMO,SAASG,0BACdC,QAAgB,EAChBC,QAAgB,EAChBxB,qBAAqC;QAGnCA;IADF,MAAMK,UAAAA,CACJL,mCAAAA,qBAAqB,CACnBM,QAAQC,GAAG,CAACC,YAAY,KAAK,SAAS,0BAAS,OAChD,CAACe,SAAS,KAAA,OAAA,KAAA,IAFXvB,iCAEaK,OAAO;IACtB,MAAMoB,aAAaZ,wBAAwBW;IAE3C,oCAAoC;IACpC,IAAI,CAACnB,SAAS;IAEd,6DAA6D;IAC7D,IAAIA,OAAO,CAACoB,WAAW,EAAE;QACvB;IACF;IAEA,yEAAyE;IACzE,OAAOC,0BAA0BX,OAAOY,IAAI,CAACtB,QAAQ,CAAC,EAAE;AAC1D;AAEA;;;CAGC,GACD,SAASQ,wBAAwBW,QAAgB;IAC/C,yMAAI5B,gBAAAA,EAAc4B,UAAU,QAAQ;QAClC,OAAOA;IACT;IAEA,OAAO,QAAQA;AACjB;AAEA;;CAEC,GACD,SAASE,0BAA0BE,UAAkB;IACnD,mMAAOjC,mBAAAA,0MAAiBE,mBAAAA,EAAiB+B,YAAY;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3717, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/lib/server-action-request-meta.ts"], "sourcesContent": ["import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { NextRequest } from '../web/exports'\nimport { ACTION_HEADER } from '../../client/components/app-router-headers'\n\nexport function getServerActionRequestMetadata(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): {\n  actionId: string | null\n  isURLEncodedAction: boolean\n  isMultipartAction: boolean\n  isFetchAction: boolean\n  isPossibleServerAction: boolean\n} {\n  let actionId: string | null\n  let contentType: string | null\n\n  if (req.headers instanceof Headers) {\n    actionId = req.headers.get(ACTION_HEADER.toLowerCase()) ?? null\n    contentType = req.headers.get('content-type')\n  } else {\n    actionId = (req.headers[ACTION_HEADER.toLowerCase()] as string) ?? null\n    contentType = req.headers['content-type'] ?? null\n  }\n\n  const isURLEncodedAction = Boolean(\n    req.method === 'POST' && contentType === 'application/x-www-form-urlencoded'\n  )\n  const isMultipartAction = Boolean(\n    req.method === 'POST' && contentType?.startsWith('multipart/form-data')\n  )\n  const isFetchAction = Boolean(\n    actionId !== undefined &&\n      typeof actionId === 'string' &&\n      req.method === 'POST'\n  )\n\n  const isPossibleServerAction = Boolean(\n    isFetchAction || isURLEncodedAction || isMultipartAction\n  )\n\n  return {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isPossibleServerAction,\n  }\n}\n\nexport function getIsPossibleServerAction(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): boolean {\n  return getServerActionRequestMetadata(req).isPossibleServerAction\n}\n"], "names": ["ACTION_HEADER", "getServerActionRequestMetadata", "req", "actionId", "contentType", "headers", "Headers", "get", "toLowerCase", "isURLEncodedAction", "Boolean", "method", "isMultipartAction", "startsWith", "isFetchAction", "undefined", "isPossibleServerAction", "getIsPossibleServerAction"], "mappings": ";;;;AAGA,SAASA,aAAa,QAAQ,6CAA4C;;AAEnE,SAASC,+BACdC,GAAoD;IAQpD,IAAIC;IACJ,IAAIC;IAEJ,IAAIF,IAAIG,OAAO,YAAYC,SAAS;QAClCH,WAAWD,IAAIG,OAAO,CAACE,GAAG,yLAACP,gBAAAA,CAAcQ,WAAW,OAAO;QAC3DJ,cAAcF,IAAIG,OAAO,CAACE,GAAG,CAAC;IAChC,OAAO;QACLJ,WAAYD,IAAIG,OAAO,yLAACL,gBAAAA,CAAcQ,WAAW,GAAG,IAAe;QACnEJ,cAAcF,IAAIG,OAAO,CAAC,eAAe,IAAI;IAC/C;IAEA,MAAMI,qBAAqBC,QACzBR,IAAIS,MAAM,KAAK,UAAUP,gBAAgB;IAE3C,MAAMQ,oBAAoBF,QACxBR,IAAIS,MAAM,KAAK,UAAA,CAAUP,eAAAA,OAAAA,KAAAA,IAAAA,YAAaS,UAAU,CAAC,sBAAA;IAEnD,MAAMC,gBAAgBJ,QACpBP,aAAaY,aACX,OAAOZ,aAAa,YACpBD,IAAIS,MAAM,KAAK;IAGnB,MAAMK,yBAAyBN,QAC7BI,iBAAiBL,sBAAsBG;IAGzC,OAAO;QACLT;QACAM;QACAG;QACAE;QACAE;IACF;AACF;AAEO,SAASC,0BACdf,GAAoD;IAEpD,OAAOD,+BAA+BC,KAAKc,sBAAsB;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3752, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/lib/fallback.ts"], "sourcesContent": ["/**\n * Describes the different fallback modes that a given page can have.\n */\nexport const enum FallbackMode {\n  /**\n   * A BLOCKING_STATIC_RENDER fallback will block the request until the page is\n   * generated. No fallback page will be rendered, and users will have to wait\n   * to render the page.\n   */\n  BLOCKING_STATIC_RENDER = 'BLOCKING_STATIC_RENDER',\n\n  /**\n   * When set to PRERENDER, a fallback page will be sent to users in place of\n   * forcing them to wait for the page to be generated. This allows the user to\n   * see a rendered page earlier.\n   */\n  PRERENDER = 'PRERENDER',\n\n  /**\n   * When set to NOT_FOUND, pages that are not already prerendered will result\n   * in a not found response.\n   */\n  NOT_FOUND = 'NOT_FOUND',\n}\n\n/**\n * The fallback value returned from the `getStaticPaths` function.\n */\nexport type GetStaticPathsFallback = boolean | 'blocking'\n\n/**\n * Parses the fallback field from the prerender manifest.\n *\n * @param fallbackField The fallback field from the prerender manifest.\n * @returns The fallback mode.\n */\nexport function parseFallbackField(\n  fallbackField: string | boolean | null | undefined\n): FallbackMode | undefined {\n  if (typeof fallbackField === 'string') {\n    return FallbackMode.PRERENDER\n  } else if (fallbackField === null) {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else if (fallbackField === false) {\n    return FallbackMode.NOT_FOUND\n  } else if (fallbackField === undefined) {\n    return undefined\n  } else {\n    throw new Error(\n      `Invalid fallback option: ${fallbackField}. Fallback option must be a string, null, undefined, or false.`\n    )\n  }\n}\n\nexport function fallbackModeToFallbackField(\n  fallback: FallbackMode,\n  page: string | undefined\n): string | false | null {\n  switch (fallback) {\n    case FallbackMode.BLOCKING_STATIC_RENDER:\n      return null\n    case FallbackMode.NOT_FOUND:\n      return false\n    case FallbackMode.PRERENDER:\n      if (!page) {\n        throw new Error(\n          `Invariant: expected a page to be provided when fallback mode is \"${fallback}\"`\n        )\n      }\n\n      return page\n    default:\n      throw new Error(`Invalid fallback mode: ${fallback}`)\n  }\n}\n\n/**\n * Parses the fallback from the static paths result.\n *\n * @param result The result from the static paths function.\n * @returns The fallback mode.\n */\nexport function parseStaticPathsResult(\n  result: GetStaticPathsFallback\n): FallbackMode {\n  if (result === true) {\n    return FallbackMode.PRERENDER\n  } else if (result === 'blocking') {\n    return FallbackMode.BLOCKING_STATIC_RENDER\n  } else {\n    return FallbackMode.NOT_FOUND\n  }\n}\n"], "names": ["FallbackMode", "parseFallbackField", "fallback<PERSON><PERSON>", "undefined", "Error", "fallbackModeToFallbackField", "fallback", "page", "parseStaticPathsResult", "result"], "mappings": "AAAA;;CAEC,GACD;;;;;;AAAO,IAAWA,eAAAA,WAAAA,GAAAA,SAAAA,YAAAA;IAChB;;;;GAIC,GAAA,YAAA,CAAA,yBAAA,GAAA;IAGD;;;;GAIC,GAAA,YAAA,CAAA,YAAA,GAAA;IAGD;;;GAGC,GAAA,YAAA,CAAA,YAAA,GAAA;WAlBeA;MAoBjB;AAaM,SAASC,mBACdC,aAAkD;IAElD,IAAI,OAAOA,kBAAkB,UAAU;QACrC,OAAA;IACF,OAAO,IAAIA,kBAAkB,MAAM;QACjC,OAAA;IACF,OAAO,IAAIA,kBAAkB,OAAO;QAClC,OAAA;IACF,OAAO,IAAIA,kBAAkBC,WAAW;QACtC,OAAOA;IACT,OAAO;QACL,MAAM,OAAA,cAEL,CAFK,IAAIC,MACR,CAAC,yBAAyB,EAAEF,cAAc,8DAA8D,CAAC,GADrG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;AACF;AAEO,SAASG,4BACdC,QAAsB,EACtBC,IAAwB;IAExB,OAAQD;QACN,KAAA;YACE,OAAO;QACT,KAAA;YACE,OAAO;QACT,KAAA;YACE,IAAI,CAACC,MAAM;gBACT,MAAM,OAAA,cAEL,CAFK,IAAIH,MACR,CAAC,iEAAiE,EAAEE,SAAS,CAAC,CAAC,GAD3E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,OAAOC;QACT;YACE,MAAM,OAAA,cAA+C,CAA/C,IAAIH,MAAM,CAAC,uBAAuB,EAAEE,UAAU,GAA9C,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;IACxD;AACF;AAQO,SAASE,uBACdC,MAA8B;IAE9B,IAAIA,WAAW,MAAM;QACnB,OAAA;IACF,OAAO,IAAIA,WAAW,YAAY;QAChC,OAAA;IACF,OAAO;QACL,OAAA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3830, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/lib/etag.ts"], "sourcesContent": ["/**\n * FNV-1a Hash implementation\n * <AUTHOR> (tjwebb) <<EMAIL>>\n *\n * Ported from https://github.com/tjwebb/fnv-plus/blob/master/index.js\n *\n * Simplified, optimized and add modified for 52 bit, which provides a larger hash space\n * and still making use of Javascript's 53-bit integer space.\n */\nexport const fnv1a52 = (str: string) => {\n  const len = str.length\n  let i = 0,\n    t0 = 0,\n    v0 = 0x2325,\n    t1 = 0,\n    v1 = 0x8422,\n    t2 = 0,\n    v2 = 0x9ce4,\n    t3 = 0,\n    v3 = 0xcbf2\n\n  while (i < len) {\n    v0 ^= str.charCodeAt(i++)\n    t0 = v0 * 435\n    t1 = v1 * 435\n    t2 = v2 * 435\n    t3 = v3 * 435\n    t2 += v0 << 8\n    t3 += v1 << 8\n    t1 += t0 >>> 16\n    v0 = t0 & 65535\n    t2 += t1 >>> 16\n    v1 = t1 & 65535\n    v3 = (t3 + (t2 >>> 16)) & 65535\n    v2 = t2 & 65535\n  }\n\n  return (\n    (v3 & 15) * 281474976710656 +\n    v2 * 4294967296 +\n    v1 * 65536 +\n    (v0 ^ (v3 >> 4))\n  )\n}\n\nexport const generateETag = (payload: string, weak = false) => {\n  const prefix = weak ? 'W/\"' : '\"'\n  return (\n    prefix + fnv1a52(payload).toString(36) + payload.length.toString(36) + '\"'\n  )\n}\n"], "names": ["fnv1a52", "str", "len", "length", "i", "t0", "v0", "t1", "v1", "t2", "v2", "t3", "v3", "charCodeAt", "generateETag", "payload", "weak", "prefix", "toString"], "mappings": "AAAA;;;;;;;;CAQC,GACD;;;;AAAO,MAAMA,UAAU,CAACC;IACtB,MAAMC,MAAMD,IAAIE,MAAM;IACtB,IAAIC,IAAI,GACNC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK,QACLC,KAAK,GACLC,KAAK;IAEP,MAAOR,IAAIF,IAAK;QACdI,MAAML,IAAIY,UAAU,CAACT;QACrBC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVC,KAAKC,KAAK;QACVH,MAAMH,MAAM;QACZK,MAAMH,MAAM;QACZD,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVI,MAAMF,OAAO;QACbC,KAAKD,KAAK;QACVK,KAAMD,KAAMF,CAAAA,OAAO,EAAC,IAAM;QAC1BC,KAAKD,KAAK;IACZ;IAEA,OACGG,CAAAA,KAAK,EAAC,IAAK,kBACZF,KAAK,aACLF,KAAK,QACJF,CAAAA,KAAMM,MAAM,CAAC;AAElB,EAAC;AAEM,MAAME,eAAe,CAACC,SAAiBC,OAAO,KAAK;IACxD,MAAMC,SAASD,OAAO,QAAQ;IAC9B,OACEC,SAASjB,QAAQe,SAASG,QAAQ,CAAC,MAAMH,QAAQZ,MAAM,CAACe,QAAQ,CAAC,MAAM;AAE3E,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3871, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/compiled/fresh/index.js"], "sourcesContent": ["(()=>{\"use strict\";var e={695:e=>{\n/*!\n * fresh\n * Copyright(c) 2012 <PERSON><PERSON>\n * Copyright(c) 2016-2017 <PERSON>\n * MIT Licensed\n */\nvar r=/(?:^|,)\\s*?no-cache\\s*?(?:,|$)/;e.exports=fresh;function fresh(e,a){var t=e[\"if-modified-since\"];var s=e[\"if-none-match\"];if(!t&&!s){return false}var i=e[\"cache-control\"];if(i&&r.test(i)){return false}if(s&&s!==\"*\"){var f=a[\"etag\"];if(!f){return false}var n=true;var u=parseTokenList(s);for(var _=0;_<u.length;_++){var o=u[_];if(o===f||o===\"W/\"+f||\"W/\"+o===f){n=false;break}}if(n){return false}}if(t){var p=a[\"last-modified\"];var v=!p||!(parseHttpDate(p)<=parseHttpDate(t));if(v){return false}}return true}function parseHttpDate(e){var r=e&&Date.parse(e);return typeof r===\"number\"?r:NaN}function parseTokenList(e){var r=0;var a=[];var t=0;for(var s=0,i=e.length;s<i;s++){switch(e.charCodeAt(s)){case 32:if(t===r){t=r=s+1}break;case 44:a.push(e.substring(t,r));t=r=s+1;break;default:r=s+1;break}}a.push(e.substring(t,r));return a}}};var r={};function __nccwpck_require__(a){var t=r[a];if(t!==undefined){return t.exports}var s=r[a]={exports:{}};var i=true;try{e[a](s,s.exports,__nccwpck_require__);i=false}finally{if(i)delete r[a]}return s.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var a=__nccwpck_require__(695);module.exports=a})();"], "names": [], "mappings": "AAAA,CAAC;IAAK;IAAa,IAAI,IAAE;QAAC,KAAI,CAAA;YAC9B;;;;;CAKC,GACD,IAAI,IAAE;YAAiC,EAAE,OAAO,GAAC;YAAM,SAAS,MAAM,CAAC,EAAC,CAAC;gBAAE,IAAI,IAAE,CAAC,CAAC,oBAAoB;gBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;gBAAC,IAAG,CAAC,KAAG,CAAC,GAAE;oBAAC,OAAO;gBAAK;gBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;gBAAC,IAAG,KAAG,EAAE,IAAI,CAAC,IAAG;oBAAC,OAAO;gBAAK;gBAAC,IAAG,KAAG,MAAI,KAAI;oBAAC,IAAI,IAAE,CAAC,CAAC,OAAO;oBAAC,IAAG,CAAC,GAAE;wBAAC,OAAO;oBAAK;oBAAC,IAAI,IAAE;oBAAK,IAAI,IAAE,eAAe;oBAAG,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAI;wBAAC,IAAI,IAAE,CAAC,CAAC,EAAE;wBAAC,IAAG,MAAI,KAAG,MAAI,OAAK,KAAG,OAAK,MAAI,GAAE;4BAAC,IAAE;4BAAM;wBAAK;oBAAC;oBAAC,IAAG,GAAE;wBAAC,OAAO;oBAAK;gBAAC;gBAAC,IAAG,GAAE;oBAAC,IAAI,IAAE,CAAC,CAAC,gBAAgB;oBAAC,IAAI,IAAE,CAAC,KAAG,CAAC,CAAC,cAAc,MAAI,cAAc,EAAE;oBAAE,IAAG,GAAE;wBAAC,OAAO;oBAAK;gBAAC;gBAAC,OAAO;YAAI;YAAC,SAAS,cAAc,CAAC;gBAAE,IAAI,IAAE,KAAG,KAAK,KAAK,CAAC;gBAAG,OAAO,OAAO,MAAI,WAAS,IAAE;YAAG;YAAC,SAAS,eAAe,CAAC;gBAAE,IAAI,IAAE;gBAAE,IAAI,IAAE,EAAE;gBAAC,IAAI,IAAE;gBAAE,IAAI,IAAI,IAAE,GAAE,IAAE,EAAE,MAAM,EAAC,IAAE,GAAE,IAAI;oBAAC,OAAO,EAAE,UAAU,CAAC;wBAAI,KAAK;4BAAG,IAAG,MAAI,GAAE;gCAAC,IAAE,IAAE,IAAE;4BAAC;4BAAC;wBAAM,KAAK;4BAAG,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE;4BAAI,IAAE,IAAE,IAAE;4BAAE;wBAAM;4BAAQ,IAAE,IAAE;4BAAE;oBAAK;gBAAC;gBAAC,EAAE,IAAI,CAAC,EAAE,SAAS,CAAC,GAAE;gBAAI,OAAO;YAAC;QAAC;IAAC;IAAE,IAAI,IAAE,CAAC;IAAE,SAAS,oBAAoB,CAAC;QAAE,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,MAAI,WAAU;YAAC,OAAO,EAAE,OAAO;QAAA;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE,GAAC;YAAC,SAAQ,CAAC;QAAC;QAAE,IAAI,IAAE;QAAK,IAAG;YAAC,CAAC,CAAC,EAAE,CAAC,GAAE,EAAE,OAAO,EAAC;YAAqB,IAAE;QAAK,SAAQ;YAAC,IAAG,GAAE,OAAO,CAAC,CAAC,EAAE;QAAA;QAAC,OAAO,EAAE,OAAO;IAAA;IAAC,IAAG,OAAO,wBAAsB,aAAY,oBAAoB,EAAE,GAAC,kFAAU;IAAI,IAAI,IAAE,oBAAoB;IAAK,OAAO,OAAO,GAAC;AAAC,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3974, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/lib/cache-control.ts"], "sourcesContent": ["import { CACHE_ONE_YEAR } from '../../lib/constants'\n\n/**\n * The revalidate option used internally for pages. A value of `false` means\n * that the page should not be revalidated. A number means that the page\n * should be revalidated after the given number of seconds (this also includes\n * `1` which means to revalidate after 1 second). A value of `0` is not a valid\n * value for this option.\n */\nexport type Revalidate = number | false\n\nexport interface CacheControl {\n  revalidate: Revalidate\n  expire: number | undefined\n}\n\nexport function getCacheControlHeader({\n  revalidate,\n  expire,\n}: CacheControl): string {\n  const swrHeader =\n    typeof revalidate === 'number' &&\n    expire !== undefined &&\n    revalidate < expire\n      ? `, stale-while-revalidate=${expire - revalidate}`\n      : ''\n\n  if (revalidate === 0) {\n    return 'private, no-cache, no-store, max-age=0, must-revalidate'\n  } else if (typeof revalidate === 'number') {\n    return `s-maxage=${revalidate}${swrHeader}`\n  }\n\n  return `s-maxage=${CACHE_ONE_YEAR}${swrHeader}`\n}\n"], "names": ["CACHE_ONE_YEAR", "getCacheControlHeader", "revalidate", "expire", "swr<PERSON><PERSON><PERSON>", "undefined"], "mappings": ";;;AAAA,SAASA,cAAc,QAAQ,sBAAqB;;AAgB7C,SAASC,sBAAsB,EACpCC,UAAU,EACVC,MAAM,EACO;IACb,MAAMC,YACJ,OAAOF,eAAe,YACtBC,WAAWE,aACXH,aAAaC,SACT,CAAC,yBAAyB,EAAEA,SAASD,YAAY,GACjD;IAEN,IAAIA,eAAe,GAAG;QACpB,OAAO;IACT,OAAO,IAAI,OAAOA,eAAe,UAAU;QACzC,OAAO,CAAC,SAAS,EAAEA,aAAaE,WAAW;IAC7C;IAEA,OAAO,CAAC,SAAS,0JAAEJ,iBAAAA,GAAiBI,WAAW;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3992, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/server/send-payload.ts"], "sourcesContent": ["import type { IncomingMessage, ServerResponse } from 'http'\nimport type RenderResult from './render-result'\nimport type { CacheControl } from './lib/cache-control'\n\nimport { isResSent } from '../shared/lib/utils'\nimport { generateETag } from './lib/etag'\nimport fresh from 'next/dist/compiled/fresh'\nimport { getCacheControlHeader } from './lib/cache-control'\nimport { RSC_CONTENT_TYPE_HEADER } from '../client/components/app-router-headers'\n\nexport function sendEtagResponse(\n  req: IncomingMessage,\n  res: ServerResponse,\n  etag: string | undefined\n): boolean {\n  if (etag) {\n    /**\n     * The server generating a 304 response MUST generate any of the\n     * following header fields that would have been sent in a 200 (OK)\n     * response to the same request: Cache-Control, Content-Location, Date,\n     * ETag, Expires, and Vary. https://tools.ietf.org/html/rfc7232#section-4.1\n     */\n    res.setHeader('ETag', etag)\n  }\n\n  if (fresh(req.headers, { etag })) {\n    res.statusCode = 304\n    res.end()\n    return true\n  }\n\n  return false\n}\n\nexport async function sendRenderResult({\n  req,\n  res,\n  result,\n  type,\n  generateEtags,\n  poweredByHeader,\n  cacheControl,\n}: {\n  req: IncomingMessage\n  res: ServerResponse\n  result: RenderResult\n  type: 'html' | 'json' | 'rsc'\n  generateEtags: boolean\n  poweredByHeader: boolean\n  cacheControl: CacheControl | undefined\n}): Promise<void> {\n  if (isResSent(res)) {\n    return\n  }\n\n  if (poweredByHeader && type === 'html') {\n    res.setHeader('X-Powered-By', 'Next.js')\n  }\n\n  // If cache control is already set on the response we don't\n  // override it to allow users to customize it via next.config\n  if (cacheControl && !res.getHeader('Cache-Control')) {\n    res.setHeader('Cache-Control', getCacheControlHeader(cacheControl))\n  }\n\n  const payload = result.isDynamic ? null : result.toUnchunkedString()\n\n  if (generateEtags && payload !== null) {\n    const etag = generateETag(payload)\n    if (sendEtagResponse(req, res, etag)) {\n      return\n    }\n  }\n\n  if (!res.getHeader('Content-Type')) {\n    res.setHeader(\n      'Content-Type',\n      result.contentType\n        ? result.contentType\n        : type === 'rsc'\n          ? RSC_CONTENT_TYPE_HEADER\n          : type === 'json'\n            ? 'application/json'\n            : 'text/html; charset=utf-8'\n    )\n  }\n\n  if (payload) {\n    res.setHeader('Content-Length', Buffer.byteLength(payload))\n  }\n\n  if (req.method === 'HEAD') {\n    res.end(null)\n    return\n  }\n\n  if (payload !== null) {\n    res.end(payload)\n    return\n  }\n\n  // Pipe the render result to the response after we get a writer for it.\n  await result.pipeToNodeResponse(res)\n}\n"], "names": ["isResSent", "generateETag", "fresh", "getCacheControlHeader", "RSC_CONTENT_TYPE_HEADER", "sendEtagResponse", "req", "res", "etag", "<PERSON><PERSON><PERSON><PERSON>", "headers", "statusCode", "end", "sendRenderResult", "result", "type", "generateEtags", "poweredByHeader", "cacheControl", "<PERSON><PERSON><PERSON><PERSON>", "payload", "isDynamic", "toUnchunkedString", "contentType", "<PERSON><PERSON><PERSON>", "byteLength", "method", "pipeToNodeResponse"], "mappings": ";;;;AAIA,SAASA,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,YAAY,QAAQ,aAAY;AACzC,OAAOC,WAAW,2BAA0B;AAC5C,SAASC,qBAAqB,QAAQ,sBAAqB;AAC3D,SAASC,uBAAuB,QAAQ,0CAAyC;;;;;;AAE1E,SAASC,iBACdC,GAAoB,EACpBC,GAAmB,EACnBC,IAAwB;IAExB,IAAIA,MAAM;QACR;;;;;KAKC,GACDD,IAAIE,SAAS,CAAC,QAAQD;IACxB;IAEA,mKAAIN,UAAAA,EAAMI,IAAII,OAAO,EAAE;QAAEF;IAAK,IAAI;QAChCD,IAAII,UAAU,GAAG;QACjBJ,IAAIK,GAAG;QACP,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAeC,iBAAiB,EACrCP,GAAG,EACHC,GAAG,EACHO,MAAM,EACNC,IAAI,EACJC,aAAa,EACbC,eAAe,EACfC,YAAY,EASb;IACC,sKAAIlB,YAAAA,EAAUO,MAAM;QAClB;IACF;IAEA,IAAIU,mBAAmBF,SAAS,QAAQ;QACtCR,IAAIE,SAAS,CAAC,gBAAgB;IAChC;IAEA,2DAA2D;IAC3D,6DAA6D;IAC7D,IAAIS,gBAAgB,CAACX,IAAIY,SAAS,CAAC,kBAAkB;QACnDZ,IAAIE,SAAS,CAAC,8LAAiBN,wBAAAA,EAAsBe;IACvD;IAEA,MAAME,UAAUN,OAAOO,SAAS,GAAG,OAAOP,OAAOQ,iBAAiB;IAElE,IAAIN,iBAAiBI,YAAY,MAAM;QACrC,MAAMZ,wKAAOP,eAAAA,EAAamB;QAC1B,IAAIf,iBAAiBC,KAAKC,KAAKC,OAAO;YACpC;QACF;IACF;IAEA,IAAI,CAACD,IAAIY,SAAS,CAAC,iBAAiB;QAClCZ,IAAIE,SAAS,CACX,gBACAK,OAAOS,WAAW,GACdT,OAAOS,WAAW,GAClBR,SAAS,gMACPX,0BAAAA,GACAW,SAAS,SACP,qBACA;IAEZ;IAEA,IAAIK,SAAS;QACXb,IAAIE,SAAS,CAAC,kBAAkBe,OAAOC,UAAU,CAACL;IACpD;IAEA,IAAId,IAAIoB,MAAM,KAAK,QAAQ;QACzBnB,IAAIK,GAAG,CAAC;QACR;IACF;IAEA,IAAIQ,YAAY,MAAM;QACpBb,IAAIK,GAAG,CAACQ;QACR;IACF;IAEA,uEAAuE;IACvE,MAAMN,OAAOa,kBAAkB,CAACpB;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4070, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport type { IncomingMessage, ServerResponse } from 'node:http'\n\nimport {\n  AppPageRouteModule,\n  type AppPageRouteHandlerContext,\n} from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\n\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\nimport { getRevalidateReason } from '../../server/instrumentation/utils'\nimport { getTracer, SpanKind, type Span } from '../../server/lib/trace/tracer'\nimport { getRequestMeta } from '../../server/request-meta'\nimport { BaseServerSpan } from '../../server/lib/trace/constants'\nimport { interopDefault } from '../../server/app-render/interop-default'\nimport { NodeNextRequest, NodeNextResponse } from '../../server/base-http/node'\nimport { checkIsAppPPREnabled } from '../../server/lib/experimental/ppr'\nimport {\n  getFallbackRouteParams,\n  type FallbackRouteParams,\n} from '../../server/request/fallback-params'\nimport { setReferenceManifestsSingleton } from '../../server/app-render/encryption-utils'\nimport {\n  isHtmlBotRequest,\n  shouldServeStreamingMetadata,\n} from '../../server/lib/streaming-metadata'\nimport { createServerModuleMap } from '../../server/app-render/action-utils'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { getIsPossibleServerAction } from '../../server/lib/server-action-request-meta'\nimport {\n  RSC_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_IS_PRERENDER_HEADER,\n  NEXT_DID_POSTPONE_HEADER,\n} from '../../client/components/app-router-headers'\nimport { getBotType, isBot } from '../../shared/lib/router/utils/is-bot'\nimport {\n  CachedRouteKind,\n  type CachedAppPageValue,\n  type CachedPageValue,\n  type ResponseCacheEntry,\n  type ResponseGenerator,\n} from '../../server/response-cache'\nimport { FallbackMode, parseFallbackField } from '../../lib/fallback'\nimport RenderResult from '../../server/render-result'\nimport { CACHE_ONE_YEAR, NEXT_CACHE_TAGS_HEADER } from '../../lib/constants'\nimport type { CacheControl } from '../../server/lib/cache-control'\nimport { ENCODED_TAGS } from '../../server/stream-utils/encoded-tags'\nimport { sendRenderResult } from '../../server/send-payload'\nimport { NoFallbackError } from '../../shared/lib/no-fallback-error.external'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nimport GlobalError from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\nexport { GlobalError }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nimport * as entryBase from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n  distDir: process.env.__NEXT_RELATIVE_DIST_DIR || '',\n  projectDir: process.env.__NEXT_RELATIVE_PROJECT_DIR || '',\n})\n\nexport async function handler(\n  req: IncomingMessage,\n  res: ServerResponse,\n  ctx: {\n    waitUntil: (prom: Promise<void>) => void\n  }\n) {\n  let srcPage = 'VAR_DEFINITION_PAGE'\n\n  // turbopack doesn't normalize `/index` in the page name\n  // so we need to to process dynamic routes properly\n  // TODO: fix turbopack providing differing value from webpack\n  if (process.env.TURBOPACK) {\n    srcPage = srcPage.replace(/\\/index$/, '') || '/'\n  } else if (srcPage === '/index') {\n    // we always normalize /index specifically\n    srcPage = '/'\n  }\n  const multiZoneDraftMode = process.env\n    .__NEXT_MULTI_ZONE_DRAFT_MODE as any as boolean\n\n  const initialPostponed = getRequestMeta(req, 'postponed')\n  // TODO: replace with more specific flags\n  const minimalMode = getRequestMeta(req, 'minimalMode')\n\n  const prepareResult = await routeModule.prepare(req, res, {\n    srcPage,\n    multiZoneDraftMode,\n  })\n\n  if (!prepareResult) {\n    res.statusCode = 400\n    res.end('Bad Request')\n    ctx.waitUntil?.(Promise.resolve())\n    return null\n  }\n\n  const {\n    buildId,\n    query,\n    params,\n    parsedUrl,\n    pageIsDynamic,\n    buildManifest,\n    nextFontManifest,\n    reactLoadableManifest,\n    serverActionsManifest,\n    clientReferenceManifest,\n    subresourceIntegrityManifest,\n    prerenderManifest,\n    isDraftMode,\n    resolvedPathname,\n    revalidateOnlyGenerated,\n    routerServerContext,\n    nextConfig,\n  } = prepareResult\n\n  const pathname = parsedUrl.pathname || '/'\n  const normalizedSrcPage = normalizeAppPath(srcPage)\n\n  let { isOnDemandRevalidate } = prepareResult\n\n  const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage]\n  const isPrerendered = prerenderManifest.routes[resolvedPathname]\n\n  let isSSG = Boolean(\n    prerenderInfo ||\n      isPrerendered ||\n      prerenderManifest.routes[normalizedSrcPage]\n  )\n\n  const userAgent = req.headers['user-agent'] || ''\n  const botType = getBotType(userAgent)\n  const isHtmlBot = isHtmlBotRequest(req)\n\n  /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */\n  const isPrefetchRSCRequest =\n    getRequestMeta(req, 'isPrefetchRSCRequest') ??\n    Boolean(req.headers[NEXT_ROUTER_PREFETCH_HEADER])\n\n  // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n\n  const isRSCRequest =\n    getRequestMeta(req, 'isRSCRequest') ?? Boolean(req.headers[RSC_HEADER])\n\n  const isPossibleServerAction = getIsPossibleServerAction(req)\n\n  /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */\n  const couldSupportPPR: boolean = checkIsAppPPREnabled(\n    nextConfig.experimental.ppr\n  )\n\n  // When enabled, this will allow the use of the `?__nextppronly` query to\n  // enable debugging of the static shell.\n  const hasDebugStaticShellQuery =\n    process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1' &&\n    typeof query.__nextppronly !== 'undefined' &&\n    couldSupportPPR\n\n  // When enabled, this will allow the use of the `?__nextppronly` query\n  // to enable debugging of the fallback shell.\n  const hasDebugFallbackShellQuery =\n    hasDebugStaticShellQuery && query.__nextppronly === 'fallback'\n\n  // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n  // prerender manifest and this is an app page.\n  const isRoutePPREnabled: boolean =\n    couldSupportPPR &&\n    ((\n      prerenderManifest.routes[normalizedSrcPage] ??\n      prerenderManifest.dynamicRoutes[normalizedSrcPage]\n    )?.renderingMode === 'PARTIALLY_STATIC' ||\n      // Ideally we'd want to check the appConfig to see if this page has PPR\n      // enabled or not, but that would require plumbing the appConfig through\n      // to the server during development. We assume that the page supports it\n      // but only during development.\n      (hasDebugStaticShellQuery &&\n        (routeModule.isDev === true ||\n          routerServerContext?.experimentalTestProxy === true)))\n\n  const isDebugStaticShell: boolean =\n    hasDebugStaticShellQuery && isRoutePPREnabled\n\n  // We should enable debugging dynamic accesses when the static shell\n  // debugging has been enabled and we're also in development mode.\n  const isDebugDynamicAccesses =\n    isDebugStaticShell && routeModule.isDev === true\n\n  const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled\n\n  // If we're in minimal mode, then try to get the postponed information from\n  // the request metadata. If available, use it for resuming the postponed\n  // render.\n  const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined\n\n  // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n  // we can use this fact to only generate the flight data for the request\n  // because we can't cache the HTML (as it's also dynamic).\n  const isDynamicRSCRequest =\n    isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest\n\n  // Need to read this before it's stripped by stripFlightHeaders. We don't\n  // need to transfer it to the request meta because it's only read\n  // within this function; the static segment data should have already been\n  // generated, so we will always either return a static response or a 404.\n  const segmentPrefetchHeader = getRequestMeta(req, 'segmentPrefetchRSCRequest')\n\n  // TODO: investigate existing bug with shouldServeStreamingMetadata always\n  // being true for a revalidate due to modifying the base-server this.renderOpts\n  // when fixing this to correct logic it causes hydration issue since we set\n  // serveStreamingMetadata to true during export\n  let serveStreamingMetadata = !userAgent\n    ? true\n    : shouldServeStreamingMetadata(userAgent, nextConfig.htmlLimitedBots)\n\n  if (isHtmlBot && isRoutePPREnabled) {\n    isSSG = false\n    serveStreamingMetadata = false\n  }\n\n  // In development, we always want to generate dynamic HTML.\n  let supportsDynamicResponse: boolean =\n    // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true ||\n    // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG ||\n    // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' ||\n    // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest\n\n  // When html bots request PPR page, perform the full dynamic rendering.\n  const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled\n\n  let ssgCacheKey: string | null = null\n  if (\n    !isDraftMode &&\n    isSSG &&\n    !supportsDynamicResponse &&\n    !isPossibleServerAction &&\n    !minimalPostponed &&\n    !isDynamicRSCRequest\n  ) {\n    ssgCacheKey = resolvedPathname\n  }\n\n  // the staticPathKey differs from ssgCacheKey since\n  // ssgCacheKey is null in dev since we're always in \"dynamic\"\n  // mode in dev to bypass the cache, but we still need to honor\n  // dynamicParams = false in dev mode\n  let staticPathKey = ssgCacheKey\n  if (!staticPathKey && routeModule.isDev) {\n    staticPathKey = resolvedPathname\n  }\n\n  const ComponentMod = {\n    ...entryBase,\n    tree,\n    pages,\n    GlobalError,\n    handler,\n    routeModule,\n    __next_app__,\n  }\n\n  // Before rendering (which initializes component tree modules), we have to\n  // set the reference manifests to our global store so Server Action's\n  // encryption util can access to them at the top level of the page module.\n  if (serverActionsManifest && clientReferenceManifest) {\n    setReferenceManifestsSingleton({\n      page: srcPage,\n      clientReferenceManifest,\n      serverActionsManifest,\n      serverModuleMap: createServerModuleMap({\n        serverActionsManifest,\n      }),\n    })\n  }\n\n  const method = req.method || 'GET'\n  const tracer = getTracer()\n  const activeSpan = tracer.getActiveScopeSpan()\n\n  try {\n    const invokeRouteModule = async (\n      span: Span | undefined,\n      context: AppPageRouteHandlerContext\n    ) => {\n      const nextReq = new NodeNextRequest(req)\n      const nextRes = new NodeNextResponse(res)\n\n      // TODO: adapt for putting the RDC inside the postponed data\n      // If we're in dev, and this isn't a prefetch or a server action,\n      // we should seed the resume data cache.\n      if (process.env.NODE_ENV === 'development') {\n        if (\n          nextConfig.experimental.dynamicIO &&\n          !isPrefetchRSCRequest &&\n          !context.renderOpts.isPossibleServerAction\n        ) {\n          const warmup = await routeModule.warmup(nextReq, nextRes, context)\n\n          // If the warmup is successful, we should use the resume data\n          // cache from the warmup.\n          if (warmup.metadata.renderResumeDataCache) {\n            context.renderOpts.renderResumeDataCache =\n              warmup.metadata.renderResumeDataCache\n          }\n        }\n      }\n\n      return routeModule.render(nextReq, nextRes, context).finally(() => {\n        if (!span) return\n\n        span.setAttributes({\n          'http.status_code': res.statusCode,\n          'next.rsc': false,\n        })\n\n        const rootSpanAttributes = tracer.getRootSpanAttributes()\n        // We were unable to get attributes, probably OTEL is not enabled\n        if (!rootSpanAttributes) {\n          return\n        }\n\n        if (\n          rootSpanAttributes.get('next.span_type') !==\n          BaseServerSpan.handleRequest\n        ) {\n          console.warn(\n            `Unexpected root span type '${rootSpanAttributes.get(\n              'next.span_type'\n            )}'. Please report this Next.js issue https://github.com/vercel/next.js`\n          )\n          return\n        }\n\n        const route = rootSpanAttributes.get('next.route')\n        if (route) {\n          const name = `${method} ${route}`\n\n          span.setAttributes({\n            'next.route': route,\n            'http.route': route,\n            'next.span_name': name,\n          })\n          span.updateName(name)\n        } else {\n          span.updateName(`${method} ${req.url}`)\n        }\n      })\n    }\n\n    const doRender = async ({\n      span,\n      postponed,\n      fallbackRouteParams,\n    }: {\n      span?: Span\n      /**\n       * The postponed data for this render. This is only provided when resuming\n       * a render that has been postponed.\n       */\n      postponed: string | undefined\n\n      /**\n       * The unknown route params for this render.\n       */\n      fallbackRouteParams: FallbackRouteParams | null\n    }): Promise<ResponseCacheEntry> => {\n      const context: AppPageRouteHandlerContext = {\n        query,\n        params,\n        page: normalizedSrcPage,\n        sharedContext: {\n          buildId,\n        },\n        serverComponentsHmrCache: getRequestMeta(\n          req,\n          'serverComponentsHmrCache'\n        ),\n        fallbackRouteParams,\n        renderOpts: {\n          App: () => null,\n          Document: () => null,\n          pageConfig: {},\n          ComponentMod,\n          Component: interopDefault(ComponentMod),\n\n          params,\n          routeModule,\n          page: srcPage,\n          postponed,\n          shouldWaitOnAllReady,\n          serveStreamingMetadata,\n          supportsDynamicResponse:\n            typeof postponed === 'string' || supportsDynamicResponse,\n          buildManifest,\n          nextFontManifest,\n          reactLoadableManifest,\n          subresourceIntegrityManifest,\n          serverActionsManifest,\n          clientReferenceManifest,\n          setIsrStatus: routerServerContext?.setIsrStatus,\n\n          dir: routeModule.projectDir,\n          isDraftMode,\n          isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n          botType,\n          isOnDemandRevalidate,\n          isPossibleServerAction,\n          assetPrefix: nextConfig.assetPrefix,\n          nextConfigOutput: nextConfig.output,\n          crossOrigin: nextConfig.crossOrigin,\n          trailingSlash: nextConfig.trailingSlash,\n          previewProps: prerenderManifest.preview,\n          deploymentId: nextConfig.deploymentId,\n          enableTainting: nextConfig.experimental.taint,\n          htmlLimitedBots: nextConfig.htmlLimitedBots,\n          devtoolSegmentExplorer:\n            nextConfig.experimental.devtoolSegmentExplorer,\n          reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n\n          multiZoneDraftMode,\n          incrementalCache: getRequestMeta(req, 'incrementalCache'),\n          cacheLifeProfiles: nextConfig.experimental.cacheLife,\n          basePath: nextConfig.basePath,\n          serverActions: nextConfig.experimental.serverActions,\n\n          ...(isDebugStaticShell || isDebugDynamicAccesses\n            ? {\n                nextExport: true,\n                supportsDynamicResponse: false,\n                isStaticGeneration: true,\n                isRevalidate: true,\n                isDebugDynamicAccesses: isDebugDynamicAccesses,\n              }\n            : {}),\n\n          experimental: {\n            isRoutePPREnabled,\n            expireTime: nextConfig.expireTime,\n            staleTimes: nextConfig.experimental.staleTimes,\n            dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n            clientSegmentCache: Boolean(\n              nextConfig.experimental.clientSegmentCache\n            ),\n            dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n            inlineCss: Boolean(nextConfig.experimental.inlineCss),\n            authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n            clientTraceMetadata:\n              nextConfig.experimental.clientTraceMetadata || ([] as any),\n          },\n\n          waitUntil: ctx.waitUntil,\n          onClose: (cb) => {\n            res.on('close', cb)\n          },\n          onAfterTaskError: () => {},\n\n          onInstrumentationRequestError: (error, _request, errorContext) =>\n            routeModule.onRequestError(\n              req,\n              error,\n              errorContext,\n              routerServerContext\n            ),\n          err: getRequestMeta(req, 'invokeError'),\n          dev: routeModule.isDev,\n        },\n      }\n\n      const result = await invokeRouteModule(span, context)\n\n      const { metadata } = result\n\n      const {\n        cacheControl,\n        headers = {},\n        // Add any fetch tags that were on the page to the response headers.\n        fetchTags: cacheTags,\n      } = metadata\n\n      if (cacheTags) {\n        headers[NEXT_CACHE_TAGS_HEADER] = cacheTags\n      }\n\n      // Pull any fetch metrics from the render onto the request.\n      ;(req as any).fetchMetrics = metadata.fetchMetrics\n\n      // we don't throw static to dynamic errors in dev as isSSG\n      // is a best guess in dev since we don't have the prerender pass\n      // to know whether the path is actually static or not\n      if (\n        isSSG &&\n        cacheControl?.revalidate === 0 &&\n        !routeModule.isDev &&\n        !isRoutePPREnabled\n      ) {\n        const staticBailoutInfo = metadata.staticBailoutInfo\n\n        const err = new Error(\n          `Page changed from static to dynamic at runtime ${resolvedPathname}${\n            staticBailoutInfo?.description\n              ? `, reason: ${staticBailoutInfo.description}`\n              : ``\n          }` +\n            `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`\n        )\n\n        if (staticBailoutInfo?.stack) {\n          const stack = staticBailoutInfo.stack\n          err.stack = err.message + stack.substring(stack.indexOf('\\n'))\n        }\n\n        throw err\n      }\n\n      return {\n        value: {\n          kind: CachedRouteKind.APP_PAGE,\n          html: result,\n          headers,\n          rscData: metadata.flightData,\n          postponed: metadata.postponed,\n          status: metadata.statusCode,\n          segmentData: metadata.segmentData,\n        } satisfies CachedAppPageValue,\n        cacheControl,\n      } satisfies ResponseCacheEntry\n    }\n\n    const responseGenerator: ResponseGenerator = async ({\n      hasResolved,\n      previousCacheEntry,\n      isRevalidating,\n      span,\n    }) => {\n      const isProduction = routeModule.isDev === false\n      const didRespond = hasResolved || res.writableEnded\n\n      // skip on-demand revalidate if cache is not present and\n      // revalidate-if-generated is set\n      if (\n        isOnDemandRevalidate &&\n        revalidateOnlyGenerated &&\n        !previousCacheEntry &&\n        !minimalMode\n      ) {\n        if (routerServerContext?.render404) {\n          await routerServerContext.render404(req, res)\n        } else {\n          res.statusCode = 404\n          res.end('This page could not be found')\n        }\n        return null\n      }\n\n      let fallbackMode: FallbackMode | undefined\n\n      if (prerenderInfo) {\n        fallbackMode = parseFallbackField(prerenderInfo.fallback)\n      }\n\n      // When serving a bot request, we want to serve a blocking render and not\n      // the prerendered page. This ensures that the correct content is served\n      // to the bot in the head.\n      if (fallbackMode === FallbackMode.PRERENDER && isBot(userAgent)) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      if (previousCacheEntry?.isStale === -1) {\n        isOnDemandRevalidate = true\n      }\n\n      // TODO: adapt for PPR\n      // only allow on-demand revalidate for fallback: true/blocking\n      // or for prerendered fallback: false paths\n      if (\n        isOnDemandRevalidate &&\n        (fallbackMode !== FallbackMode.NOT_FOUND || previousCacheEntry)\n      ) {\n        fallbackMode = FallbackMode.BLOCKING_STATIC_RENDER\n      }\n\n      if (\n        !minimalMode &&\n        fallbackMode !== FallbackMode.BLOCKING_STATIC_RENDER &&\n        staticPathKey &&\n        !didRespond &&\n        !isDraftMode &&\n        pageIsDynamic &&\n        (isProduction || !isPrerendered)\n      ) {\n        // if the page has dynamicParams: false and this pathname wasn't\n        // prerendered trigger the no fallback handling\n        if (\n          // In development, fall through to render to handle missing\n          // getStaticPaths.\n          (isProduction || prerenderInfo) &&\n          // When fallback isn't present, abort this render so we 404\n          fallbackMode === FallbackMode.NOT_FOUND\n        ) {\n          throw new NoFallbackError()\n        }\n\n        let fallbackResponse: ResponseCacheEntry | null | undefined\n\n        if (isRoutePPREnabled && !isRSCRequest) {\n          // We use the response cache here to handle the revalidation and\n          // management of the fallback shell.\n          fallbackResponse = await routeModule.handleResponse({\n            cacheKey: isProduction ? normalizedSrcPage : null,\n            req,\n            nextConfig,\n            routeKind: RouteKind.APP_PAGE,\n            isFallback: true,\n            prerenderManifest,\n            isRoutePPREnabled,\n            responseGenerator: async () =>\n              doRender({\n                span,\n                // We pass `undefined` as rendering a fallback isn't resumed\n                // here.\n                postponed: undefined,\n                fallbackRouteParams:\n                  // If we're in production or we're debugging the fallback\n                  // shell then we should postpone when dynamic params are\n                  // accessed.\n                  isProduction || isDebugFallbackShell\n                    ? getFallbackRouteParams(normalizedSrcPage)\n                    : null,\n              }),\n            waitUntil: ctx.waitUntil,\n          })\n\n          // If the fallback response was set to null, then we should return null.\n          if (fallbackResponse === null) return null\n\n          // Otherwise, if we did get a fallback response, we should return it.\n          if (fallbackResponse) {\n            // Remove the cache control from the response to prevent it from being\n            // used in the surrounding cache.\n            delete fallbackResponse.cacheControl\n\n            return fallbackResponse\n          }\n        }\n      }\n      // Only requests that aren't revalidating can be resumed. If we have the\n      // minimal postponed data, then we should resume the render with it.\n      const postponed =\n        !isOnDemandRevalidate && !isRevalidating && minimalPostponed\n          ? minimalPostponed\n          : undefined\n\n      // When we're in minimal mode, if we're trying to debug the static shell,\n      // we should just return nothing instead of resuming the dynamic render.\n      if (\n        (isDebugStaticShell || isDebugDynamicAccesses) &&\n        typeof postponed !== 'undefined'\n      ) {\n        return {\n          cacheControl: { revalidate: 1, expire: undefined },\n          value: {\n            kind: CachedRouteKind.PAGES,\n            html: RenderResult.fromStatic(''),\n            pageData: {},\n            headers: undefined,\n            status: undefined,\n          } satisfies CachedPageValue,\n        }\n      }\n\n      // If this is a dynamic route with PPR enabled and the default route\n      // matches were set, then we should pass the fallback route params to\n      // the renderer as this is a fallback revalidation request.\n      const fallbackRouteParams =\n        pageIsDynamic &&\n        isRoutePPREnabled &&\n        (getRequestMeta(req, 'renderFallbackShell') || isDebugFallbackShell)\n          ? getFallbackRouteParams(pathname)\n          : null\n\n      // Perform the render.\n      return doRender({\n        span,\n        postponed,\n        fallbackRouteParams,\n      })\n    }\n\n    const handleResponse = async (span?: Span): Promise<null | void> => {\n      const cacheEntry = await routeModule.handleResponse({\n        cacheKey: ssgCacheKey,\n        responseGenerator: (c) =>\n          responseGenerator({\n            span,\n            ...c,\n          }),\n        routeKind: RouteKind.APP_PAGE,\n        isOnDemandRevalidate,\n        isRoutePPREnabled,\n        req,\n        nextConfig,\n        prerenderManifest,\n        waitUntil: ctx.waitUntil,\n      })\n\n      if (isDraftMode) {\n        res.setHeader(\n          'Cache-Control',\n          'private, no-cache, no-store, max-age=0, must-revalidate'\n        )\n      }\n\n      // In dev, we should not cache pages for any reason.\n      if (routeModule.isDev) {\n        res.setHeader('Cache-Control', 'no-store, must-revalidate')\n      }\n\n      if (!cacheEntry) {\n        if (ssgCacheKey) {\n          // A cache entry might not be generated if a response is written\n          // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n          // have a cache key. If we do have a cache key but we don't end up\n          // with a cache entry, then either Next.js or the application has a\n          // bug that needs fixing.\n          throw new Error('invariant: cache entry required but not generated')\n        }\n        return null\n      }\n\n      if (cacheEntry.value?.kind !== CachedRouteKind.APP_PAGE) {\n        throw new Error(\n          `Invariant app-page handler received invalid cache entry ${cacheEntry.value?.kind}`\n        )\n      }\n\n      const didPostpone = typeof cacheEntry.value.postponed === 'string'\n\n      if (\n        isSSG &&\n        // We don't want to send a cache header for requests that contain dynamic\n        // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n        // request, then we should set the cache header.\n        !isDynamicRSCRequest &&\n        (!didPostpone || isPrefetchRSCRequest)\n      ) {\n        if (!minimalMode) {\n          // set x-nextjs-cache header to match the header\n          // we set for the image-optimizer\n          res.setHeader(\n            'x-nextjs-cache',\n            isOnDemandRevalidate\n              ? 'REVALIDATED'\n              : cacheEntry.isMiss\n                ? 'MISS'\n                : cacheEntry.isStale\n                  ? 'STALE'\n                  : 'HIT'\n          )\n        }\n        // Set a header used by the client router to signal the response is static\n        // and should respect the `static` cache staleTime value.\n        res.setHeader(NEXT_IS_PRERENDER_HEADER, '1')\n      }\n      const { value: cachedData } = cacheEntry\n\n      // Coerce the cache control parameter from the render.\n      let cacheControl: CacheControl | undefined\n\n      // If this is a resume request in minimal mode it is streamed with dynamic\n      // content and should not be cached.\n      if (minimalPostponed) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      }\n\n      // If this is in minimal mode and this is a flight request that isn't a\n      // prefetch request while PPR is enabled, it cannot be cached as it contains\n      // dynamic content.\n      else if (\n        minimalMode &&\n        isRSCRequest &&\n        !isPrefetchRSCRequest &&\n        isRoutePPREnabled\n      ) {\n        cacheControl = { revalidate: 0, expire: undefined }\n      } else if (!routeModule.isDev) {\n        // If this is a preview mode request, we shouldn't cache it\n        if (isDraftMode) {\n          cacheControl = { revalidate: 0, expire: undefined }\n        }\n\n        // If this isn't SSG, then we should set change the header only if it is\n        // not set already.\n        else if (!isSSG) {\n          if (!res.getHeader('Cache-Control')) {\n            cacheControl = { revalidate: 0, expire: undefined }\n          }\n        } else if (cacheEntry.cacheControl) {\n          // If the cache entry has a cache control with a revalidate value that's\n          // a number, use it.\n          if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n            if (cacheEntry.cacheControl.revalidate < 1) {\n              throw new Error(\n                `Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`\n              )\n            }\n\n            cacheControl = {\n              revalidate: cacheEntry.cacheControl.revalidate,\n              expire: cacheEntry.cacheControl?.expire ?? nextConfig.expireTime,\n            }\n          }\n          // Otherwise if the revalidate value is false, then we should use the\n          // cache time of one year.\n          else {\n            cacheControl = { revalidate: CACHE_ONE_YEAR, expire: undefined }\n          }\n        }\n      }\n\n      cacheEntry.cacheControl = cacheControl\n\n      if (\n        typeof segmentPrefetchHeader === 'string' &&\n        cachedData?.kind === CachedRouteKind.APP_PAGE &&\n        cachedData.segmentData\n      ) {\n        // This is a prefetch request issued by the client Segment Cache. These\n        // should never reach the application layer (lambda). We should either\n        // respond from the cache (HIT) or respond with 204 No Content (MISS).\n\n        // Set a header to indicate that PPR is enabled for this route. This\n        // lets the client distinguish between a regular cache miss and a cache\n        // miss due to PPR being disabled. In other contexts this header is used\n        // to indicate that the response contains dynamic data, but here we're\n        // only using it to indicate that the feature is enabled — the segment\n        // response itself contains whether the data is dynamic.\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '2')\n\n        // Add the cache tags header to the response if it exists and we're in\n        // minimal mode while rendering a static page.\n        const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n        if (minimalMode && isSSG && tags && typeof tags === 'string') {\n          res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n        }\n\n        const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader)\n        if (matchedSegment !== undefined) {\n          // Cache hit\n          return sendRenderResult({\n            req,\n            res,\n            type: 'rsc',\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: RenderResult.fromStatic(matchedSegment),\n            cacheControl: cacheEntry.cacheControl,\n          })\n        }\n\n        // Cache miss. Either a cache entry for this route has not been generated\n        // (which technically should not be possible when PPR is enabled, because\n        // at a minimum there should always be a fallback entry) or there's no\n        // match for the requested segment. Respond with a 204 No Content. We\n        // don't bother to respond with 404, because these requests are only\n        // issued as part of a prefetch.\n        res.statusCode = 204\n        return sendRenderResult({\n          req,\n          res,\n          type: 'rsc',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.fromStatic(''),\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If there's a callback for `onCacheEntry`, call it with the cache entry\n      // and the revalidate options.\n      const onCacheEntry = getRequestMeta(req, 'onCacheEntry')\n      if (onCacheEntry) {\n        const finished = await onCacheEntry(\n          {\n            ...cacheEntry,\n            // TODO: remove this when upstream doesn't\n            // always expect this value to be \"PAGE\"\n            value: {\n              ...cacheEntry.value,\n              kind: 'PAGE',\n            },\n          },\n          {\n            url: getRequestMeta(req, 'initURL'),\n          }\n        )\n        if (finished) {\n          // TODO: maybe we have to end the request?\n          return null\n        }\n      }\n\n      // If the request has a postponed state and it's a resume request we\n      // should error.\n      if (didPostpone && minimalPostponed) {\n        throw new Error(\n          'Invariant: postponed state should not be present on a resume request'\n        )\n      }\n\n      if (cachedData.headers) {\n        const headers = { ...cachedData.headers }\n\n        if (!minimalMode || !isSSG) {\n          delete headers[NEXT_CACHE_TAGS_HEADER]\n        }\n\n        for (let [key, value] of Object.entries(headers)) {\n          if (typeof value === 'undefined') continue\n\n          if (Array.isArray(value)) {\n            for (const v of value) {\n              res.appendHeader(key, v)\n            }\n          } else if (typeof value === 'number') {\n            value = value.toString()\n            res.appendHeader(key, value)\n          } else {\n            res.appendHeader(key, value)\n          }\n        }\n      }\n\n      // Add the cache tags header to the response if it exists and we're in\n      // minimal mode while rendering a static page.\n      const tags = cachedData.headers?.[NEXT_CACHE_TAGS_HEADER]\n      if (minimalMode && isSSG && tags && typeof tags === 'string') {\n        res.setHeader(NEXT_CACHE_TAGS_HEADER, tags)\n      }\n\n      // If the request is a data request, then we shouldn't set the status code\n      // from the response because it should always be 200. This should be gated\n      // behind the experimental PPR flag.\n      if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n        res.statusCode = cachedData.status\n      }\n\n      // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n      if (\n        !minimalMode &&\n        cachedData.status &&\n        RedirectStatusCode[cachedData.status] &&\n        isRSCRequest\n      ) {\n        res.statusCode = 200\n      }\n\n      // Mark that the request did postpone.\n      if (didPostpone) {\n        res.setHeader(NEXT_DID_POSTPONE_HEADER, '1')\n      }\n\n      // we don't go through this block when preview mode is true\n      // as preview mode is a dynamic request (bypasses cache) and doesn't\n      // generate both HTML and payloads in the same request so continue to just\n      // return the generated payload\n      if (isRSCRequest && !isDraftMode) {\n        // If this is a dynamic RSC request, then stream the response.\n        if (typeof cachedData.rscData === 'undefined') {\n          if (cachedData.postponed) {\n            throw new Error('Invariant: Expected postponed to be undefined')\n          }\n\n          return sendRenderResult({\n            req,\n            res,\n            type: 'rsc',\n            generateEtags: nextConfig.generateEtags,\n            poweredByHeader: nextConfig.poweredByHeader,\n            result: cachedData.html,\n            // Dynamic RSC responses cannot be cached, even if they're\n            // configured with `force-static` because we have no way of\n            // distinguishing between `force-static` and pages that have no\n            // postponed state.\n            // TODO: distinguish `force-static` from pages with no postponed state (static)\n            cacheControl: isDynamicRSCRequest\n              ? { revalidate: 0, expire: undefined }\n              : cacheEntry.cacheControl,\n          })\n        }\n\n        // As this isn't a prefetch request, we should serve the static flight\n        // data.\n        return sendRenderResult({\n          req,\n          res,\n          type: 'rsc',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: RenderResult.fromStatic(cachedData.rscData),\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // This is a request for HTML data.\n      let body = cachedData.html\n\n      // If there's no postponed state, we should just serve the HTML. This\n      // should also be the case for a resume request because it's completed\n      // as a server render (rather than a static render).\n      if (!didPostpone || minimalMode) {\n        return sendRenderResult({\n          req,\n          res,\n          type: 'html',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: cacheEntry.cacheControl,\n        })\n      }\n\n      // If we're debugging the static shell or the dynamic API accesses, we\n      // should just serve the HTML without resuming the render. The returned\n      // HTML will be the static shell so all the Dynamic API's will be used\n      // during static generation.\n      if (isDebugStaticShell || isDebugDynamicAccesses) {\n        // Since we're not resuming the render, we need to at least add the\n        // closing body and html tags to create valid HTML.\n        body.chain(\n          new ReadableStream({\n            start(controller) {\n              controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n              controller.close()\n            },\n          })\n        )\n\n        return sendRenderResult({\n          req,\n          res,\n          type: 'html',\n          generateEtags: nextConfig.generateEtags,\n          poweredByHeader: nextConfig.poweredByHeader,\n          result: body,\n          cacheControl: { revalidate: 0, expire: undefined },\n        })\n      }\n\n      // This request has postponed, so let's create a new transformer that the\n      // dynamic data can pipe to that will attach the dynamic data to the end\n      // of the response.\n      const transformer = new TransformStream<Uint8Array, Uint8Array>()\n      body.chain(transformer.readable)\n\n      // Perform the render again, but this time, provide the postponed state.\n      // We don't await because we want the result to start streaming now, and\n      // we've already chained the transformer's readable to the render result.\n      doRender({\n        span,\n        postponed: cachedData.postponed,\n        // This is a resume render, not a fallback render, so we don't need to\n        // set this.\n        fallbackRouteParams: null,\n      })\n        .then(async (result) => {\n          if (!result) {\n            throw new Error('Invariant: expected a result to be returned')\n          }\n\n          if (result.value?.kind !== CachedRouteKind.APP_PAGE) {\n            throw new Error(\n              `Invariant: expected a page response, got ${result.value?.kind}`\n            )\n          }\n\n          // Pipe the resume result to the transformer.\n          await result.value.html.pipeTo(transformer.writable)\n        })\n        .catch((err) => {\n          // An error occurred during piping or preparing the render, abort\n          // the transformers writer so we can terminate the stream.\n          transformer.writable.abort(err).catch((e) => {\n            console.error(\"couldn't abort transformer\", e)\n          })\n        })\n\n      return sendRenderResult({\n        req,\n        res,\n        type: 'html',\n        generateEtags: nextConfig.generateEtags,\n        poweredByHeader: nextConfig.poweredByHeader,\n        result: body,\n        // We don't want to cache the response if it has postponed data because\n        // the response being sent to the client it's dynamic parts are streamed\n        // to the client on the same request.\n        cacheControl: { revalidate: 0, expire: undefined },\n      })\n    }\n\n    // TODO: activeSpan code path is for when wrapped by\n    // next-server can be removed when this is no longer used\n    if (activeSpan) {\n      await handleResponse(activeSpan)\n    } else {\n      return await tracer.withPropagatedContext(req.headers, () =>\n        tracer.trace(\n          BaseServerSpan.handleRequest,\n          {\n            spanName: `${method} ${req.url}`,\n            kind: SpanKind.SERVER,\n            attributes: {\n              'http.method': method,\n              'http.target': req.url,\n            },\n          },\n          handleResponse\n        )\n      )\n    }\n  } catch (err) {\n    // if we aren't wrapped by base-server handle here\n    if (!activeSpan && !(err instanceof NoFallbackError)) {\n      await routeModule.onRequestError(\n        req,\n        err,\n        {\n          routerKind: 'App Router',\n          routePath: srcPage,\n          routeType: 'render',\n          revalidateReason: getRevalidateReason({\n            isRevalidate: isSSG,\n            isOnDemandRevalidate,\n          }),\n        },\n        routerServerContext\n      )\n    }\n\n    // rethrow so that we can handle serving error page\n    throw err\n  }\n}\n"], "names": ["AppPageRouteModule", "RouteKind", "getRevalidateReason", "getTracer", "SpanKind", "getRequestMeta", "BaseServerSpan", "interopDefault", "NodeNextRequest", "NodeNextResponse", "checkIsAppPPREnabled", "getFallbackRouteParams", "setReferenceManifestsSingleton", "isHtmlBotRequest", "shouldServeStreamingMetadata", "createServerModuleMap", "normalizeAppPath", "getIsPossibleServerAction", "RSC_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_IS_PRERENDER_HEADER", "NEXT_DID_POSTPONE_HEADER", "getBotType", "isBot", "CachedRouteKind", "FallbackMode", "parseFallbackField", "RenderResult", "CACHE_ONE_YEAR", "NEXT_CACHE_TAGS_HEADER", "ENCODED_TAGS", "sendRenderResult", "NoFallbackError", "tree", "pages", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "entryBase", "RedirectStatusCode", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree", "distDir", "process", "env", "__NEXT_RELATIVE_DIST_DIR", "projectDir", "__NEXT_RELATIVE_PROJECT_DIR", "handler", "req", "res", "ctx", "prerenderManifest", "srcPage", "TURBOPACK", "replace", "multiZoneDraftMode", "__NEXT_MULTI_ZONE_DRAFT_MODE", "initialPostponed", "minimalMode", "prepareResult", "prepare", "statusCode", "end", "waitUntil", "Promise", "resolve", "buildId", "query", "params", "parsedUrl", "pageIsDynamic", "buildManifest", "nextFontManifest", "reactLoadableManifest", "serverActionsManifest", "clientReferenceManifest", "subresourceIntegrityManifest", "isDraftMode", "resolvedPathname", "revalidateOnlyGenerated", "routerServerContext", "nextConfig", "normalizedSrcPage", "isOnDemandRevalidate", "prerenderInfo", "dynamicRoutes", "isP<PERSON>endered", "routes", "isSSG", "Boolean", "userAgent", "headers", "botType", "isHtmlBot", "isPrefetchRSCRequest", "isRSCRequest", "isPossibleServerAction", "couldSupportPPR", "experimental", "ppr", "hasDebugStaticShellQuery", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "__nextppronly", "hasDebugFallbackShellQuery", "isRoutePPREnabled", "renderingMode", "isDev", "experimentalTestProxy", "isDebugStaticShell", "isDebugDynamicAccesses", "isDebugFallbackShell", "minimalPostponed", "undefined", "isDynamicRSCRequest", "segmentPrefetchHeader", "serveStreamingMetadata", "htmlLimitedBots", "supportsDynamicResponse", "shouldWaitOnAllReady", "ssgCacheKey", "static<PERSON><PERSON><PERSON><PERSON>", "ComponentMod", "serverModuleMap", "method", "tracer", "activeSpan", "getActiveScopeSpan", "invokeRouteModule", "span", "context", "nextReq", "nextRes", "NODE_ENV", "dynamicIO", "renderOpts", "warmup", "metadata", "renderResumeDataCache", "render", "finally", "setAttributes", "rootSpanAttributes", "getRootSpanAttributes", "get", "handleRequest", "console", "warn", "route", "name", "updateName", "url", "doR<PERSON>", "postponed", "fallbackRouteParams", "sharedContext", "serverComponentsHmrCache", "App", "Document", "pageConfig", "Component", "setIsrStatus", "dir", "isRevalidate", "assetPrefix", "nextConfigOutput", "output", "crossOrigin", "trailingSlash", "previewProps", "preview", "deploymentId", "enableTainting", "taint", "devtoolSegmentExplorer", "reactMaxHeadersLength", "incrementalCache", "cacheLifeProfiles", "cacheLife", "basePath", "serverActions", "nextExport", "isStaticGeneration", "expireTime", "staleTimes", "clientSegmentCache", "dynamicOnHover", "inlineCss", "authInterrupts", "clientTraceMetadata", "onClose", "cb", "on", "onAfterTaskError", "onInstrumentationRequestError", "error", "_request", "errorContext", "onRequestError", "err", "dev", "result", "cacheControl", "fetchTags", "cacheTags", "fetchMetrics", "revalidate", "staticBailoutInfo", "Error", "description", "stack", "message", "substring", "indexOf", "value", "html", "rscData", "flightData", "status", "segmentData", "responseGenerator", "hasResolved", "previousCacheEntry", "isRevalidating", "isProduction", "didRespond", "writableEnded", "render404", "fallbackMode", "fallback", "PRERENDER", "BLOCKING_STATIC_RENDER", "isStale", "NOT_FOUND", "fallbackResponse", "handleResponse", "cache<PERSON>ey", "routeKind", "<PERSON><PERSON><PERSON><PERSON>", "expire", "PAGES", "fromStatic", "pageData", "cacheEntry", "cachedData", "c", "<PERSON><PERSON><PERSON><PERSON>", "didPostpone", "isMiss", "<PERSON><PERSON><PERSON><PERSON>", "tags", "matchedSegment", "type", "generateEtags", "poweredByHeader", "onCacheEntry", "finished", "key", "Object", "entries", "Array", "isArray", "v", "append<PERSON><PERSON>er", "toString", "body", "chain", "ReadableStream", "start", "controller", "enqueue", "CLOSED", "BODY_AND_HTML", "close", "transformer", "TransformStream", "readable", "then", "pipeTo", "writable", "catch", "abort", "e", "withPropagatedContext", "trace", "spanName", "SERVER", "attributes", "routerKind", "routePath", "routeType", "revalidateReason"], "mappings": ";;;;;;;AAGA,SACEA,kBAAkB,QAEb,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IAE7C,wBAAwB;AAEnF,SAASE,mBAAmB,QAAQ,qCAAoC;AAExE,SAASG,cAAc,QAAQ,4BAA2B;AAE1D,SAASE,cAAc,QAAQ,0CAAyC;AAExE,SAASG,oBAAoB,QAAQ,oCAAmC;AAKxE,SAASE,8BAA8B,QAAQ,2CAA0C;AAMzF,SAASI,gBAAgB,QAAQ,0CAAyC;AAQ1E,SAASM,UAAU,EAAEC,KAAK,QAAQ,uCAAsC;AACxE,SACEC,eAAe,QAKV,8BAA6B;AACpC,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,qBAAoB;AACrE,OAAOC,kBAAkB,6BAA4B;AACrD,SAASC,cAAc,EAAEC,sBAAsB,QAAQ,sBAAqB;AAE5E,SAASC,YAAY,QAAQ,yCAAwC;AACrE,SAASC,gBAAgB,QAAQ,4BAA2B;AAC5D,SAASC,eAAe,QAAQ,8CAA6C;AAW7E,yEAAyE;AACzE,UAAU;AACV,cAAc;AACd,eAAe;AAEf,SAASC,IAAI,EAAEC,KAAK,GAAE;AAEtB,OAAOC,iBAAiB,+BAA+B;;IAAE,wBAAwB;;AAAsB,EAAC;AAExG,SAASA,WAAW,GAAE;AAMtB,8BAA8B;AAC9B,iCAAiC;AAEjC,OAAO,MAAMC,eAAe;IAC1BC,SAASC;IACTC,WAAWC;;IAwCX,OAAO,IAAIyB,YAAY,UAAU;IAGjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1CF,EAAC,uEAAA;AAED,UAAA,EAAYxB,eAAe,0CAA0C;IAAE,EAAA,OAAA;IAAA;IAAA,UAAwB;QAAsB,EAAC,UAAA;YAAA;YAAA,CACtH;YAAA,MAASC,kBAAkB,QAAQ,+CAA8C;gBAEjF,UAAA,CAAc,CAAA;gBAAA,QAAA;oBAAA,IAAA,sBAA0C;oBAAA;iBAAA;;WAAE,wBAAwB;IAAsB,EAAC;IAAA;QAEzG,UAAA;YAAA,MAAA,qCAA4D;gBACrD,MAAMC,CAAAA,QAAAA;wBAAAA,EAAc,IAAI3C,mBAAmB;4BAChD4C,KAAAA,CAAAA,GAAAA,4MAAAA,CAAAA,GAAY,mBAAA,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,MAAM5C,CAAAA,GAAAA,MAAU6C,QAAQ,+RAAlB7C,CAAAA,UAAkB,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACxB8C,MAAM,CAAA,YAAA,CAAA;;qBACNC,UAAU;gBACV,2CAA2C;;UAC3CC,QAAAA;YAAAA,GAAY,CAAA;YAAA;SAAA;cACZC,OAAAA;YAAAA,EAAU,EAAA;YAAA;SAAA;cACVC,OAAAA;YAAAA,EAAU,EAAE;YAAA;SAAA;UACd,cAAA;YAAA,IAAA;YAAA;SAAA;UACAC,UAAU,IAAA;YAAA,IAAA;YAAA;SAAA;;OACRC,YAAYpB;IACd,EAAA,QAAA;IAAA;CAAA;;;;IAYA,EAAA,EAAIgC,UAAU,WAAA,sBAAA,CAAA;IAEd,EAAA,0BAAA,sBAAA,CAAA,KAAwD;AACxD,MAAA,eAAA,2BAAmD;IACnD,SAAA,oDAA6D;IAC7D,IAAIV,OAAAA,CAAQC,GAAG,CAACU,SAAS,EAAE;QACzBD,UAAUA,QAAQE,OAAO,CAAC,YAAY,OAAO;;;;AAY/C,GAAMK,GAAAA,aAAgB,CAAA,IAAA,sMAAA,CAAA,CAAM7B,YAAY8B,QAAAA,CAAAA,CAAO,CAACZ,KAAKC,KAAK;QACxDG,QAAAA;QACAG,MAAAA,+LAAAA,CAAAA,YAAAA,CAAAA,QAAAA;QACF,MAAA;QAEI,CAACI,SAAAA,MAAe;QAClBV,IAAIY,UAAU,GAAG,0BAAA;QACjBZ,IAAIa,GAAG,CAAC,IAAA;QACRZ,IAAIa,MAAAA,GAAS,oBAAbb,IAAIa,SAAS,MAAbb,KAAgBc,QAAQC,OAAO;QAC/B,OAAO,GAAA,EAAA;IACT;IAEA,MAAM,EACJC,EAAAA,KAAO,EACPC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,4BAA4B,EAC5BzB,iBAAiB,EACjB0B,WAAW,EACXC,gBAAgB,EAChBC,uBAAuB,EACvBC,mBAAmB,EACnBC,UAAU,EACX,GAAGtB;QAEJ,EAAMxB,UAAAA,CAAWkC,UAAUlC,QAAQ,IAAI;IACvC,MAAM+C,oBAAoB/E,iBAAiBiD;IAE3C,IAAI,EAAE+B,GAAAA,iBAAoB,EAAE,GAAGxB,uBAAAA;IAE/B,MAAMyB,MAAAA,UAAgBjC,kBAAkBkC,YAAcH,CAAD,CAACA,eAAkB;IACxE,MAAMI,gBAAgBnC,kBAAkBoC,MAAM,CAACT,iBAAiB;AAEhE,CAAIU,QAAQC,MAAAA,EACVL,MAAAA,GAAAA,EAAAA,GAAAA,EAAAA,CACEE,EAAAA,eACAnC,kBAAkBoC,MAAM,CAACL,kBAAkB;IAG/C,IAAA,EAAMQ,YAAY1C,IAAI2C,OAAO,CAAC,aAAa,IAAI;IAC/C,IAAA,EAAMC,QAAAA,EAAUnF,WAAWiF;IAC3B,MAAMG,YAAY7F,iBAAiBgD,qBAAAA;IAEnC,mDAAA;;;QAIA,IAAM8C,MAAAA,QAAAA,OAAAA,CAAAA,CACJtG,WAAAA,IAAewD,GAAAA,EAAK,2BACpByC,QAAQzC,IAAI2C,OAAO,CAACrF,4BAA4B;IAElD,OAAA,IAAA,YAAA,UAAA,sDAAuF;QAEvF,EAAMyF,eACJvG,eAAewD,KAAK,KAAA,cAAmByC,QAAQzC,IAAI2C,OAAO,CAACtF,WAAW;QAExE,EAAM2F,QAAAA,iBAAyB5F,0BAA0B4C;IAEzD;;;IAGC,EACD,MAAMiD,kBAA2BpG,eAAAA,MAC/BoF,WAAWiB,YAAY,CAACC,GAAG;IAG7B,MAAA,cAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,iCAAyE;IACzE,MAAA,gBAAA,MAAA,YAAwC,OAAA,CAAA,KAAA,KAAA;QACxC,EAAMC,2BACJ1D,QAAQC,GAAG,CAAC0D,0CAA0C,KAAK,OAC3D,OAAOlC,MAAMmC,aAAa,KAAK,eAC/BL;QAEF,kEAAsE;IACtE,6CAA6C;IAC7C,IAAA,CAAA,CAAMM,cAAAA,eACJH,4BAA4BjC,MAAMmC,aAAa,KAAK;QAEtD,IAAA,UAAA,GAAA,uDAA4E;QAC5E,IAAA,GAAA,CAAA,kCAA8C;QAC9C,EAAME,EAAAA,SAAAA,IAAAA,KACJP,EAAAA,KAAAA,IAAAA,IAAAA,IACC,CAAA,EACC9C,EAAAA,CAAAA,IAAAA,CAAAA,KAAAA,QAAAA,KAAkBoC,EAAAA,IAAM,CAACL,kBAAkB,IAC3C/B,kBAAkBkC,aAAa,CAACH,kBAAkB,qBAFnD,AACC/B,MAECsD,aAAa,MAAK,sBACnB,uEAAuE;QACvE,OAAA,6DAAwE;IACxE,wEAAwE;IACxE,MAAA,EAAA,OAAA,EAAA,KAAA,EAAA,MAAA,CAA+B,CAAA,SAAA,EAAA,aAAA,EAAA,aAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,4BAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,uBAAA,EAAA,mBAAA,EAAA,UAAA,EAAA,GAAA;IAC9BL,MAAAA,WAAAA,UAAAA,CACEtE,CAAAA,MAAAA,IAAAA,EAAY4E,KAAK,KAAK,QACrB1B,CAAAA,uCAAAA,oBAAqB2B,qBAAqB,MAAK,IAAG,CAAE;IAE5D,MAAMC,oBAAAA,CAAAA,GAAAA,uLAAAA,CAAAA,CACJR,kBAAAA,EAAAA,YAA4BI;IAE9B,IAAA,EAAA,oBAAA,EAAA,GAAA,qCAAoE;IACpE,MAAA,gBAAA,kBAAA,aAAA,CAAA,WAAiE,OAAA;IACjE,MAAMK,gBAAAA,SACJD,SAAAA,MAAAA,CAAAA,MAAsB9E,WAAAA,CAAY4E,KAAK,KAAK;IAE9C,IAAA,EAAMI,MAAAA,QAAAA,SAAuBP,QAAAA,iBAAAA,KAA8BC,aAAAA,MAAAA,CAAAA,kBAAAA;IAE3D,MAAA,YAAA,IAAA,OAAA,CAAA,aAAA,IAAA,4BAA2E;IAC3E,MAAA,UAAA,CAAA,GAAA,oMAAA,CAAA,aAAA,EAAA,6CAAwE;IACxE,MAAA,IAAU,QAAA,CAAA,GAAA,6KAAA,CAAA,mBAAA,EAAA;IACV,MAAMO,mBAAmBP,oBAAoB/C,mBAAmBuD;;;IAIhE,EAAA,MAAA,uBAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,OAA0D,oBAAA,QAAA,IAAA,OAAA,CAAA,uLAAA,CAAA,8BAAA,CAAA;IAC1D,MAAMC,sBACJT,qBAAqBT,gBAAgB,CAACD,qBAAAA;IAExC,MAAA,eAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,mBAAA,QAAA,IAAA,CAAyE,MAAA,CAAA,uLAAA,CAAA,aAAA,CAAA;IACzE,MAAA,yBAAA,CAAA,GAAA,2LAAA,CAAA,4BAAA,EAAA,QAAiE;IACjE,yEAAyE;;;IAIzE,EAAA,MAAA,kBAAA,CAAA,GAAA,2KAAA,CAAA,uBAAA,EAAA,WAAA,YAAA,CAAA,GAA0E;IAC1E,yEAAA,MAA+E;IAC/E,wCAAA,mCAA2E;IAC3E,MAAA,2BAAA,cAA+C,8BAAA,OAAA,OAAA,MAAA,aAAA,KAAA,eAAA;IAC/C,IAAIqB,yBAAyB,CAACzB,YAC1B,OACAzF,qBAAAA,QAA6ByF,WAAWT,WAAWmC,eAAe;IAEtE,IAAIvB,aAAaW,mBAAmB,SAAA;QAClChB,EAAAA,MAAQ,uBAAA,4BAAA,MAAA,aAAA,KAAA;QACR2B,yBAAyB,+CAAA;IAC3B,8CAAA;IAEA,MAAA,oBAAA,mBAAA,CAAA,CAAA,CAAA,QAAA,GAA2D,eAAA,MAAA,CAAA,kBAAA,IAAA,kBAAA,aAAA,CAAA,kBAAA,KAAA,OAAA,KAAA,IAAA,MAAA,aAAA,MAAA,sBAAA,uEAAA;IAC3D,IAAIE,0BACF,0CAAA,6BAAuE;IACvE,6DAA6D,WAAA;IAC7DvF,YAAY4E,KAAK,KAAK,QACtB,CAAA,oEAAqE;IACrE,gBAAgB,YAAA,CAAA,YAAA,KAAA,KAAA,QAAA,CAAA,uBAAA,OAAA,KAAA,IAAA,oBAAA,qBAAA,MAAA,IAAA,CAAA;IAChB,CAAClB,KAAAA,IACD,iBAAA,4BAAA,sBAAmE;IACnE,QAAQ,4DAAA;IACR,OAAO/B,qBAAqB,YAC5B,yBAAA,6CAAsE;IACtE,MAAA,iBAAuB,QAAA,sBAAA,YAAA,KAAA,KAAA;IACvBwD,MAAAA,uBAAAA,8BAAAA;IAEF,uEAAuE,IAAA;IACvE,MAAMK,uBAAuBzB,aAAaW,8BAAAA;IAE1C,IAAIe,MAAAA,QAA6B;IACjC,IACE,CAAC1C,CAAAA,cACDW,KAAAA,IACA,CAAC6B,eAAAA,YACD,CAACrB,MAAAA,oBACD,CAACe,oBACD,CAACE,qBACD;QACAM,cAAczC,wDAAAA;IAChB,wEAAA;IAEA,mDAAmD,OAAA;IACnD,MAAA,sBAAA,qBAAA,YAA6D,IAAA,CAAA;IAC7D,8DAA8D,WAAA;IAC9D,oCAAoC,6BAAA;IACpC,IAAI0C,gBAAgBD,qDAAAA;IACpB,IAAI,CAACC,iBAAiB1F,YAAY4E,KAAK,EAAE,gCAAA;QACvCc,EAAAA,cAAgB1C,UAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,iBAAAA,EAAAA,KAAAA;IAClB,0EAAA;IAEA,MAAM2C,eAAe,0DAAA;QACnB,GAAG7F,SAAS,2DAAA;QACZR,2CAAAA;QACAC,yBAAAA,CAAAA,YAAAA,OAAAA,CAAAA,GAAAA,6KAAAA,CAAAA,+BAAAA,EAAAA,WAAAA,WAAAA,eAAAA;QACAC,aAAAA,mBAAAA;QACAyB,QAAAA;QACAjB,yBAAAA;QACAP;IACF,2DAAA;IAEA,IAAA,0BACA,4CAD0E,iBAC1E,QAAqE;IACrE,YAAA,KAAA,KAAA,QAAA,4CAA0E,yBAAA;IAC1E,IAAImD,YAAAA,aAAyBC,yBAAyB;QACpD5E,MAAAA,yBAA+B,0CAAA;YAC7BmC,MAAMkB;YACNuB,oBAAAA,YAAAA,sEAAAA;YACAD,eAAAA;YACAgD,iBAAiBxH,sBAAsB;gBACrCwE,2DAAAA;YACF,qBAAA,aAAA;QACF,cAAA;IACF,IAAA,CAAA,eAAA,SAAA,CAAA,2BAAA,CAAA,0BAAA,CAAA,oBAAA,CAAA,qBAAA;QAEA,EAAMiD,SAAS3E,GAAAA,CAAI2E,MAAM,IAAI;IAC7B,MAAMC,SAAStI;IACf,MAAMuI,aAAaD,OAAOE,kBAAkB,OAAA;IAE5C,IAAI,yDAAA;QACF,MAAMC,oBAAoB,OACxBC,MACAC,mBAAAA;YAEA,MAAMC,UAAU,IAAIvI,QAAAA,QAAgBqD;YACpC,MAAMmF,MAAAA,IAAU,IAAIvI,iBAAiBqD;YAErC,cAAA,YAAA,KAAA,EAAA,2BAA4D;YAC5D,YAAA,qDAAiE;YACjE,wCAAwC;YACxC,IAAIP,QAAQC,CAAAA,EAAG,CAACyF,QAAQ,KAAK,eAAe;gBAC1C,2MACEnD,WAAWiB,YAAY,CAACmC,SAAS,IACjC,CAACvC,wBACD,CAACmC,QAAQK,UAAU,CAACtC,sBAAsB,EAC1C;oBACA,MAAMuC,SAAS,MAAMzG,YAAYyG,MAAM,CAACL,SAASC,SAASF;oBAE1D,6DAA6D;sPAC7D,yBAAyB;oBACzB,IAAIM,OAAOC,QAAQ,CAACC,qBAAqB,EAAE;wBACzCR,QAAQK,UAAU,CAACG,qBAAqB,GACtCF,OAAOC,QAAQ,CAACC,qBAAqB;oBACzC;gBACF;YACF,kEAAA;YAEA,OAAO3G,YAAY4G,MAAM,CAACR,SAASC,SAASF,SAASU,OAAO,CAAC;gBAC3D,IAAI,CAACX,MAAM,mDAAA;gBAEXA,KAAKY,YAAAA,CAAa,CAAC,uBAAA;8MACjB,qBAAA,EAAA,CAAoB3F,IAAIY,UAAU;oBAClC,YAAY;gBACd;gBAEA,MAAMgF,qBAAqBjB,OAAOkB,qBAAqB;gBACvD,aAAA,CAAA,GAAA,iLAAA,CAAA,wBAAA,EAAA,8BAAiE;gBACjE,IAAI,CAACD,oBAAoB;oBACvB;gBACF;gBAEA,IACEA,mBAAmBE,GAAG,CAAC,sBACvBtJ,eAAeuJ,aAAa,EAC5B;oBACAC,GAAAA,KAAQC,CAAAA,GAAI,CACV,CAAC,2BAA2B,EAAEL,mBAAmBE,GAAG,CAClD,kBACA,qEAAqE,CAAC;gMAE1E,WAAA;gBACF,OAAA,OAAA,kBAAA;gBAEA,MAAMI,QAAQN,mBAAmBE,GAAG,CAAC;gBACrC,IAAII,OAAO,OAAA,OAAA,MAAA;oBACT,MAAMC,EAAAA,IAAAA,qKAAAA,CAAAA,CAAO,GAAGzB,OAAO,CAAC,EAAEwB,IAAAA,CAAAA,KAAO;oBAEjCnB,KAAKY,GAAAA,IAAAA,qKAAAA,CAAAA,MAAa,CAAC,YAAA,CAAA;wBACjB,cAAcO,kCAAAA;wBACd,cAAcA,uCAAAA;wBACd,kBAAkBC,UAAAA;oBACpB,gCAAA;oBACApB,KAAKqB,MAAAA,IAAU,CAACD,OAAAA,CAAAA,SAAAA,IAAAA,CAAAA,wBAAAA,CAAAA,QAAAA,UAAAA,CAAAA,sBAAAA,EAAAA;oBAClB,GAAO,GAAA,SAAA,MAAA,YAAA,MAAA,CAAA,SAAA,SAAA;oBACLpB,KAAKqB,UAAU,CAAC,GAAG1B,OAAO,CAAC,EAAE3E,IAAIsG,GAAG,EAAE,uBAAA;oBACxC,yBAAA;oBACF,IAAA,OAAA,QAAA,CAAA,qBAAA,EAAA;wBACF,QAAA,UAAA,CAAA,qBAAA,GAAA,OAAA,QAAA,CAAA,qBAAA;oBAEMC,KAAW,OAAO,EACtBvB,IAAI,EACJwB,SAAS,EACTC,mBAAmB,EAapB;gBACC,EAAMxB,UAAsC;gBAC1C9D;gBACAC,GAAAA,YAAAA,MAAAA,CAAAA,SAAAA,SAAAA,SAAAA,OAAAA,CAAAA;gBACAlC,IAAAA,CAAAA,CAAMgD,KAAAA;gBACNwE,KAAAA,UAAe,GAAA,CAAA;oBACbxF,oBAAAA,IAAAA,UAAAA;oBACF,YAAA;gBACAyF,0BAA0BnK,eACxBwD,KACA;gBAEFyG,MAAAA,qBAAAA,OAAAA,qBAAAA;gBACAnB,YAAY,qDAAA;oBACVsB,CAAAA,IAAK,IAAM,YAAA;oBACXC,UAAU,IAAM;oBAChBC,YAAY,CAAC;oBACbrC,mBAAAA,GAAAA,CAAAA,sBAAAA,0KAAAA,CAAAA,iBAAAA,CAAAA,aAAAA,EAAAA;oBACAsC,QAAAA,GAAWrK,CAAAA,CAAAA,CAAAA,YAAe+H,eAAAA,EAAAA,mBAAAA,GAAAA,CAAAA,kBAAAA,qEAAAA,CAAAA;oBAE1BrD;oBACAtC;oBACAI,EAAAA,IAAMkB,IAAAA,mBAAAA,GAAAA,CAAAA;oBACNoG,OAAAA;oBACAlC,MAAAA,OAAAA,GAAAA,OAAAA,CAAAA,EAAAA,OAAAA;oBACAH,KAAAA,aAAAA,CAAAA;wBACAE,cAAAA,OACE,OAAOmC,cAAc,YAAYnC;wBACnC9C,cAAAA;wBACAC,kBAAAA;oBACAC;oBACAG,KAAAA,UAAAA,CAAAA;oBACAF,GAAAA;oBACAC,KAAAA,UAAAA,CAAAA,GAAAA,OAAAA,CAAAA,EAAAA,IAAAA,GAAAA,EAAAA;oBACAqF,YAAY,EAAEhF,uCAAAA,oBAAqBgF,YAAY;oBAE/CC,KAAKnI,YAAYe,UAAU;oBAC3BgC;oBACAqF,KAAAA,OAAAA,EAAc1E,IAAAA,EAAAA,GAAS,CAACgE,KAAAA,EAAAA,MAAa,CAACvC,YAAAA,EAAAA;oBACtCrB,QAAAA;oBACAT;oBACAa;oBACAmE,EAAAA,WAAalF,WAAWkF,WAAW;oBACnCC,WAAAA,OAAkBnF,WAAWoF,MAAM;oBACnCC,aAAarF,WAAWqF,WAAW;oBACnCC,eAAetF,WAAWsF,aAAa;oBACvCC,cAAcrH,QAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,UAAkBsH,OAAAA,EAAAA,EAAO,GAAA;oBACvCC,cAAczF,WAAWyF,YAAY;oBACrCC,QAAAA,QAAgB1F,WAAWiB,YAAY,CAAC0E,KAAK;oBAC7CxD,KAAAA,IAAAA,QAAiBnC,WAAWmC,eAAe;oBAC3CyD,UAAAA,IAAAA,UACE5F,WAAWiB,YAAY,CAAC2E,sBAAsB;oBAChDC,YAAAA,CAAAA,UAAuB7F,WAAW6F,qBAAqB;oBAEvDvH;oBACAwH,WAAAA,CAAAA,GAAAA,oLAAAA,CAAAA,OAAkBvL,UAAAA,EAAAA,OAAewD,KAAK;oBACtCgI,mBAAmB/F,WAAWiB,YAAY,CAAC+E,SAAS;oBACpDC,UAAUjG,WAAWiG,QAAQ;oBAC7BC,MAAAA,SAAelG,WAAWiB,YAAY,CAACiF,aAAa;oBAEpD,GAAIvE,sBAAsBC,yBACtB;wBACEuE,YAAY;wBACZ/D,yBAAyB;wBACzBgE,oBAAoB,CAAA,OAAA,cAAA,YAAA;wBACpBnB,cAAc;wBACdrD,wBAAwBA;oBAC1B,IACA,CAAC,CAAC;oBAENX,cAAc;wBACZM;wBACA8E,YAAYrG,WAAWqG,UAAU;wBACjCC,UAAAA,EAAYtG,WAAWiB,UAAAA,EAAY,CAACqF,IAAAA,KAAAA,CAAU,GAAA,oBAAA,YAAA;wBAC9ClD,CAAAA,UAAW5C,EAAAA,MAAQR,IAAAA,OAAWiB,YAAY,CAACmC,SAAS;wBACpDmD,oBAAoB/F,QAClBR,WAAWiB,YAAY,CAACsF,kBAAkB;wBAE5CC,UAAAA,MAAgBhG,GAAAA,CAAAA,IAAQR,SAAAA,CAAAA,CAAWiB,YAAY,CAACuF,cAAc;wBAC9DC,WAAWjG,QAAQR,WAAWiB,YAAY,CAACwF,SAAS;wBACpDC,gBAAgBlG,QAAQR,WAAWiB,YAAY,CAACyF,cAAc;wBAC9DC,qBACE3G,WAAWiB,YAAY,CAAC0F,mBAAmB,IAAK,EAAE;oBACtD,aAAA,WAAA,WAAA;oBAEA7H,WAAWb,IAAIa,GAAAA,MAAS,KAAA,MAAA;oBACxB8H,SAAS,CAACC,GAAAA,WAAAA,WAAAA;wBACR7I,IAAI8I,EAAE,CAAC,IAAA,KAASD,MAAAA,aAAAA;oBAClB,cAAA,kBAAA,OAAA;oBACAE,cAAAA,IAAkB,KAAO,EAAA,YAAA;oBAEzBC,gBAAAA,WAAAA,IAA+B,CAACC,OAAOC,CAAAA,KAAAA,IAAUC,eAC/CtK,YAAYuK,cAAc,CACxBrJ,KACAkJ,OACAE,cACApH;oBAEJsH,KAAK9M,YAAAA,GAAewD,KAAK,GAAA,eAAA;oBACzBuJ,KAAKzK,YAAY4E,KAAK,EAAA,WAAA,YAAA,CAAA,sBAAA;oBACxB,uBAAA,WAAA,qBAAA;oBACF;oBAEM8F,OAAS,MAAMzE,KAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,iBAAkBC,EAAAA,IAAMC,CAAAA;oBAErCO,QAAQ,EAAE,GAAGgE,MAAAA,WAAAA,YAAAA,CAAAA,SAAAA;oBAGnBC,UAAAA,EAAY,EACZ9G,OAAAA,GAAU,CAAC,CAAC,EACZ,CAAA,mEAAoE;oBACpE+G,GAAWC,SAAS,EACrB,CAAA,EAAGnE,SAAAA,YAAAA,CAAAA,aAAAA;oBAEAmE,GAAAA,IAAW,kBAAA,yBAAA;wBACL3L,YAAAA,WAAuB,GAAG2L;wBACpC,yBAAA;wBAEA,oBAAA,2BAA2D;;wBAC7CC,IAAY,GAAGpE,SAASoE,QAAAA,IAAY;oBAElD,IAAA,CAAA,CAAA,4CAA0D;oBAC1D,cAAA,0CAAgE;wBAChE,yCAAqD;wBAEnDpH,CACAiH,CAAAA,UAAAA,WAAAA,UAAAA,CAAAA,aAAcI,UAAU,MAAK,KAC7B,CAAC/K,YAAY4E,KAAK,IAClB,CAACF,mBACD;wBACMsG,YAAAA,MAAoBtE,KAAAA,IAASsE,QAAAA,CAAAA,QAAiB,EAAA;wBAE9CR,IAAM,OAAA,QAAA,MAOX,CAPW,IAAIS,MACd,CAAC,KAAA,CAAA,SAAA,gCAA+C,EAAEjI,mBAChDgI,CAAAA,qCAAAA,kBAAmBE,WAAW,IAC1B,CAAC,UAAU,EAAEF,kBAAkBE,WAAW,EAAE,GAC5C,EAAE,EACN,GACA,CAAC,4EAA4E,CAAC,GANtE,qBAAA;2BAAA,iBAAA,QAAA,WAAA,YAAA,CAAA,kBAAA;gCAAA,QAAA,QAAA,WAAA,YAAA,CAAA,cAAA;kCAAA,CAAA,QAAA,WAAA,YAAA,CAAA,SAAA;wBAOZ,gBAAA,QAAA,WAAA,YAAA,CAAA,cAAA;wBAEIF,qBAAAA,WAAAA,CAAAA,WAAAA,CAAAA,MAAmBG,KAAK,EAAE,MAAA,IAAA,EAAA;oBAC5B,MAAMA,QAAQH,kBAAkBG,KAAK;oBACrCX,IAAIW,KAAK,EAAA,CAAGX,GAAAA,CAAIY,OAAO,CAAA,EAAGD,MAAME,SAAS,CAACF,MAAMG,OAAO,CAAC;oBAC1D,SAAA,CAAA;wBAEMd,IAAAA,EAAAA,CAAAA,SAAAA;oBACR;oBAEO,kBAAA,KAAA;oBACLe,GAAO,4BAAA,CAAA,OAAA,UAAA,eAAA,YAAA,cAAA,CAAA,KAAA,OAAA,cAAA;oBACLrL,KAAAA,CAAAA,AAAMrB,GAANqB,gKAAAA,CAAAA,iBAAMrB,EAAAA,EAAgBsB,GAAAA,KAAQ;oBAC9BqL,KAAAA,CAAMd,WAAAA,KAAAA;oBACN7G;oBACA4H,SAAS/E,SAASgF,UAAU;oBAC5BhE,OAAAA,IAAWhB,EAAAA,OAASgB,SAAS,EAAA,MAAA;oBAC7BiE,QAAQjF,EAAAA,GAAAA,IAAS3E,UAAU;oBAC3B6J,YAAAA,CAAalF,CAAAA,QAASkF,EAAAA,CAAAA,CAAAA,MACxB,CADmC,MACnC,SAAA,EAAA,GAAA;gBACAjB,WAAAA;gBACF,OAAA,CAAA,uJAAA,CAAA,yBAAA,CAAA,GAAA;YACF;YAEA,EAAMkB,oBAAuC,OAAO,EAClDC,WAAW,EACXC,eAAAA,GAAkB,EAClBC,cAAc,EACd9F,IAAI,EACL;;YAEC,IAAA,EAAMgG,UAAAA,GAAaJ,SAAAA,MAAe3K,IAAIgL,EAAAA,WAAa;YAEnD,wDAAwD,EAAA;YACxD,iCAAiC,+BAAA;YACjC,IACE9I,wBACAJ,yBAAAA,EACA,CAAC8I,sBACD,CAACnK,aACD;gBACA,IAAIsB,KAAAA,CAAAA,gBAAAA,OAAAA,KAAAA,IAAAA,CAAAA,YAAAA,QAAqBkJ,EAAAA,MAAAA,CAAS,EAAE,EAAA,CAAA,YAAA,KAAA,IAAA,CAAA,mBAAA;oBAClC,EAAA,IAAMlJ,gBAAAA,IAAoBkJ,KAAAA,IAAS,CAAClL,KAAKC,OAAAA;gBAC3C,MAAA,CAAO,KAAA,OAAA,cAAA,CAAA,IAAA,MAAA,CAAA,+CAAA,EAAA,mBAAA,CAAA,qBAAA,OAAA,KAAA,IAAA,kBAAA,WAAA,IAAA,CAAA,UAAA,EAAA,kBAAA,WAAA,EAAA,GAAA,EAAA,EAAA,GAAA,CAAA,4EAAA,CAAA,GAAA,qBAAA;oBACLA,IAAIY,GAAAA,OAAU,GAAG;oBACjBZ,IAAIa,GAAG,CAAC,IAAA;oBACV,cAAA;gBACA,OAAO;gBACT,IAAA,qBAAA,OAAA,KAAA,IAAA,kBAAA,KAAA,EAAA;oBAEIqK,MAAAA,QAAAA,kBAAAA,KAAAA;oBAEA/I,IAAAA,KAAAA,EAAe,CAAA,IAAA,OAAA,GAAA,MAAA,SAAA,CAAA,MAAA,OAAA,CAAA;gBACjB+I,eAAetN,mBAAmBuE,cAAcgJ,QAAQ;gBAC1D,MAAA;YAEA,yEAAyE;YACzE,OAAA,iEAAwE;gBACxE,OAAA,eAA0B;oBACtBD,MAAAA,2KAAAA,CAAAA,OAAiBvN,WAAAA,CAAAA,IAAayN,IAAAA,KAAS,IAAI3N,MAAMgF,YAAY;oBAC/DyI,MAAAA,KAAevN,aAAa0N,sBAAsB;oBACpD;oBAEIT,SAAAA,SAAAA,UAAAA,OAAAA,mBAAoBU,OAAO,MAAK,CAAC,GAAG;oBACtCpJ,WAAAA,QAAuB,CAAA,SAAA;oBACzB,QAAA,SAAA,UAAA;oBAEA,aAAA,CAAsB,QAAA,WAAA;gBACtB,0DAA8D;gBAC9D,uCAA2C;YAC3C,IACEA,wBACCgJ,CAAAA,iBAAiBvN,aAAa4N,SAAS,IAAIX,kBAAiB,GAC7D;gBACAM,eAAevN,aAAa0N,sBAAsB;YACpD,EAAA,oBAAA,OAAA,EAAA,WAAA,EAAA,kBAAA,EAAA,cAAA,EAAA,IAAA,EAAA;YAEA,IACE,CAAC5K,CAAAA,cACDyK,CAAAA,YAAAA,IAAiBvN,CAAAA,KAAAA,OAAa0N,sBAAsB,IACpD9G,iBACA,CAACwG,cACD,CAACnJ,eACDP,iBACCyJ,CAAAA,gBAAgB,CAACzI,aAAY,GAC9B;gBACA,EAAA,aAAA,eAAA,IAAA,aAAA,iBAAgE;gBAChE,+CAA+C,KAAA;gBAC/C,IAGE,AAFA,yBAAA,kCAA2D;gBAC3D,kBAAkB,MAAA,2BAAA,CAAA,sBAAA,CAAA,aAAA;gBACjByI,CAAAA,GAAAA,aAAgB3I,UAAAA,GAAY,IAAA,CAC7B,IAAA,IAAA,oBAAA,SAAA,EAAA,oBAA2D;oBAC3D+I,MAAAA,OAAiBvN,aAAa4N,SAAS,CAAA,CACvC,IAAA;oBACA,GAAA,GAAM,IAAIrN;oBACZ,IAAA,UAAA,GAAA;oBAEIsN,IAAAA,GAAAA,CAAAA;gBAEJ,IAAIjI,qBAAqB,CAACT,cAAc;oBACtC,GAAA,6DAAgE;oBAChE,oCAAoC;oBACpC0I,mBAAmB,MAAM3M,YAAY4M,cAAc,CAAC;wBAClDC,OAAAA,GAAUZ,eAAe7I,oBAAoB;wBAC7ClC,OAAAA,CAAAA,GAAAA,sJAAAA,CAAAA,qBAAAA,EAAAA,cAAAA,QAAAA;wBACAiC;wBACA2J,WAAWxP,UAAU6C,QAAQ,gCAAA;wBAC7B4M,YAAY,gDAAA;wBACZ1L,cAAAA;wBACAqD,SAAAA,sJAAAA,CAAAA,eAAAA,CAAAA,SAAAA,IAAAA,CAAAA,GAAAA,oMAAAA,CAAAA,QAAAA,EAAAA,YAAAA;wBACAmH,OAAAA,sJAAAA,CAAAA,eAAmB,CAAA,SACjBpE,SAAS,IAAA;gCACPvB;gCACA,OAAA,OAAA,KAAA,IAAA,mBAAA,OAAA,MAAA,CAAA,GAAA,CAA4D;gCAC5D,OAAA,CAAQ;gCACRwB,WAAWxC;gCACXyC,EAAAA,mBACE,yDAAyD;gCACzD,0CAAA,cAAwD;gCACxD,YAAY,WAAA;gCACZsE,QAAAA,CAAAA,OAAgBjH,UAAAA,sJAAAA,CAAAA,eAAAA,CACZhH,SAAAA,IAAAA,UAAuBoF,QAAAA,GAAAA,UACvB;4BACR,GAAA,sJAAA,CAAA,eAAA,CAAA,sBAAA;wBACFnB,WAAWb,IAAIa,SAAS;oBAC1B,YAAA,iBAAA,sJAAA,CAAA,eAAA,CAAA,sBAAA,IAAA,iBAAA,CAAA,cAAA,CAAA,eAAA,iBAAA,CAAA,gBAAA,CAAA,aAAA,GAAA;oBAEA,4DAAA,YAAwE;oBACxE,IAAI0K,qBAAqB,MAAM,OAAO,KAAA;oBAEtC,IACA,IAAIA,UAAAA,QAAkB,2CAD+C;wBAEnE,SAAA,aAAA,KAAA,2CAAsE,gBAAA;wBACtE,SAAA,sJAAA,CAAA,eAAA,CAAA,SAAA,EAAiC;wBACjC,EAAA,IAAA,6OAAA,CAAA,CAAOA,iBAAAA,GAAiBhC,YAAY;wBAEpC,OAAOgC;oBACT;gBACF,IAAA,qBAAA,CAAA,cAAA;oBACF,gEAAA;oBACA,oCAAA,4BAAwE;oBACxE,mBAAA,MAAA,YAAA,cAAA,CAAA,QAAoE;wBAC9DjF,MACJ,CAACrE,GAAAA,eAAAA,MAAwB,CAAC2I,aAAAA,KAAkB/G,mBACxCA,mBACAC;wBAEN,6DAAyE;wBACzE,4DAAwE;wBAErEJ,WAAAA,IAAsBC,2LAAtBD,CAAAA,YAAsBC,CAAAA,QAAAA,QAAqB,KAC5C,OAAO2C,cAAc,aACrB;wBACO,YAAA;wBACLiD,UAAc;wBAAEI,YAAY;wBAAGiC,QAAQ9H,WAAAA,UAAAA,SAAAA;gCAAU;gCAC1C,4DAAA;gCACCrG,QAAAA,MAAgBoO,KAAK;gCACrBjO,WAAakO,UAAU,CAAC;gCAC9BC,EAAU,CAAC,kBACXtJ,CAASqB,uDAAAA;gCACDA,YAAAA;gCACV,gBAAA,uBAAA,CAAA,GAAA,8KAAA,CAAA,yBAAA,EAAA,qBAAA;4BACF;wBACF,WAAA,IAAA,SAAA;oBAEA,4DAAoE;oBACpE,6DAAqE,WAAA;oBACrE,IAAA,qBAAA,MAAA,OAAA,aAA2D;oBACrDyC,oBACJnF,iBACAkC,qBACChH,CAAAA,UAAAA,KAAewD,KAAK,0BAA0B8D,oBAAmB,IAC9DhH,uBAAuBqC,YACvB;oBAEN,IAAA,UAAsB,QAAA;wBACfoH,IAAS,kEAAA;wBACdvB,iCAAAA;wBACAwB,OAAAA,iBAAAA,YAAAA;wBACAC,OAAAA;oBACF;gBACF;YAEA,EAAMiF,iBAAiB,OAAO1G;gBAyCxBkH,mBA6MSC,iDAAAA;YArPb,MAAMD,aAAa,MAAMpN,YAAY4M,cAAc,CAAC,gBAAA;gBAClDC,EAAAA,QAAUpH,IAAAA,CAAAA,wBAAAA,CAAAA,kBAAAA,mBAAAA,mBAAAA;gBACVoG,mBAAmB,CAACyB,IAClBzB,kBAAkB,2BAAA;wBAChB3F,4DAAAA;wBACA,GAAGoH,CAAC,WAAA,sBAAA,KAAA,OAAA,cAAA,aAAA;oBACN,GAAA;oBACFR,OAAWxP,OAAAA,GAAU6C,QAAQ;wBAC7BkD,YAAAA;wBACAqB,QAAAA;oBACAxD;oBACAiC,OAAAA;wBACA9B,MAAAA,2KAAAA,CAAAA,kBAAAA,CAAAA,KAAAA;wBACAY,GAAWb,GAAAA,iKAAAA,CAAAA,CAAIa,SAAS,CAAA,UAAA,CAAA;wBAC1B,UAAA,CAAA;wBAEIc,KAAa,IAAA;wBACXwK,KAAS,CACX,EAAA,eACA;oBAEJ;gBAEA,gDAAoD;YACpD,IAAIvN,YAAY4E,KAAK,EAAE;gBACrBzD,IAAIoM,SAAS,CAAC,iBAAiB,iCAAA;YACjC,qEAAA;YAEA,IAAI,CAACH,YAAY,0CAAA;gBACf,EAAA,EAAI3H,aAAa,OAAA,iBAAA,qBAAA,CAAA,CAAA,GAAA,gKAAA,CAAA,iBAAA,EAAA,KAAA,0BAAA,oBAAA,IAAA,CAAA,GAAA,8KAAA,CAAA,yBAAA,EAAA,YAAA;oBACf,cAAA,kDAAgE;oBAChE,QAAA,4DAAoE;oBACpE,kEAAkE;oBAClE,mEAAmE;oBACnE,yBAAyB;oBACzB,MAAM,qBAA8D,CAA9D,IAAIwF,MAAM,sDAAV,qBAAA;+BAAA;oCAAA,EAAA;sCAAA;oBAA6D,WAAA,MAAA,YAAA,cAAA,CAAA;gBACrE,UAAA;gBACA,OAAO,YAAA,CAAA,IAAA,kBAAA;wBACT;wBAEImC,GAAAA,CAAAA,UAAAA,WAAW7B,KAAK,qBAAhB6B,kBAAkBlN,IAAI,MAAKrB,gBAAgBsB,QAAQ,EAAE;oBAEMiN;gBAD7D,MAAM,KAAA,+LAAA,CAAA,YAAA,CAAA,MAEL,CAFK,CAAA,GAAInC,MACR,CAAC,wDAAwD,GAAEmC,qBAAAA,WAAW7B,KAAK,qBAAhB6B,mBAAkBlN,IAAI,EAAE,GAD/E,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;gBACF;gBAEA,EAAMsN,SAAAA,IAAAA,CAAc,OAAOJ,CAAAA,UAAW7B,KAAK,CAAC7D,SAAS,KAAK;YAE1D,IACEhE,SACA,yEAAyE;YACzE,IAAA,aAAA,iDAAkE;gBAClE,IAAA,SAAA,CAAA,iBAAA,aAAgD;YAChD,CAACyB,uBACA,CAAA,CAACqI,eAAexJ,oBAAmB,GACpC;gBACA,IAAI,CAACpC,aAAa,8BAAA;oBAChB,QAAA,KAAA,EAAA,iCAAgD;oBAChD,SAAA,CAAA,iBAAA,MAAiC;oBACjCT,IAAIoM,SAAS,CACX,kBACAlK,uBACI,gBACA+J,WAAWK,MAAM,GACf,SACAL,WAAWX,OAAO,GAChB,UACA;gBAEZ,CAAA,YAAA;gBACA,IAAA,aAAA,yDAA0E;oBAC1E,qDAAyD,WAAA;oBACrDc,SAAS,CAAC9O,0BAA0B,gCAAA;oBAC1C,kEAAA;oBACQ8M,OAAO8B,UAAU,EAAE,GAAGD,6CAAAA;oBAE9B,yBAAA,qBAAsD;oBAClDzC,MAAAA,OAAAA,cAAAA,CAAAA,IAAAA,MAAAA,sDAAAA,qBAAAA;wBAEJ,OAAA,uDAA0E;wBAC1E,YAAA,YAAoC;wBAChC1F,UAAkB,IAAA;oBACpB0F,WAAe;oBAAEI,YAAY;oBAAGiC,GAAAA,KAAQ9H;gBAAU;YACpD,IAAA,CAAA,CAAA,CAKK,IACHtD,eACAqC,WAAAA,KACA,CAACD,IAAAA,OAAAA,KAAAA,IAAAA,IACDU,cAAAA,IAAAA,CACA,KAAA,2KAAA,CAAA,kBAAA,CAAA,QAAA,EAAA;gBACAiG,IAAAA,WAAe;oBAAEI,EAAAA,OAAAA,GAAY,WAAA,CAAA,IAAA,MAAA,CAAA,wDAAA,EAAA,CAAA,qBAAA,WAAA,KAAA,KAAA,OAAA,KAAA,IAAA,mBAAA,IAAA,EAAA,GAAA,qBAAA;oBAAGiC,OAAAA,CAAQ9H;oBAAU,YAAA;oBAC7C,GAAI,CAAClF,UAAAA,EAAY4E,KAAK,EAAE;gBAC7B,2DAA2D;gBAC3D,IAAI7B,aAAa;oBACf4H,YAAAA,GAAe,IAAA,WAAA,KAAA,CAAA,SAAA,KAAA;wBAAEI,CAAAA,WAAY,8DAAA;wBAAGiC,QAAQ9H,8CAAAA;oBAAU,wCAAA;gBACpD,OAIK,IAAI,CAACxB,OAAO,CAAA,CAAA,CAAA,eAAA,oBAAA,GAAA;oBACf,CAAA,GAAI,CAACvC,IAAIuM,KAAAA,IAAS,CAAC,kBAAkB;wBACnC/C,eAAe,6BAAA;4BAAEI,YAAY,aAAA;4BAAGiC,KAAAA,CAAAA,EAAQ9H,gBAAAA,uBAAAA,gBAAAA,WAAAA,MAAAA,GAAAA,SAAAA,WAAAA,OAAAA,GAAAA,UAAAA;wBAAU;oBACpD,sEAAA;gBACF,OAAO,IAAIkI,WAAWzC,YAAY,EAAE,qBAAA;oBAClC,SAAA,CAAA,uLAAA,CAAA,2BAAA,EAAA,oCAAwE;oBACxE,oBAAoB;oBACpB,IAAI,GAAA,IAAOyC,MAAAA,EAAAA,GAAWzC,YAAY,CAACI,UAAU,KAAK,UAAU;4BAShDqC,sCAAAA;wBARV,IAAIA,WAAWzC,YAAY,CAACI,UAAU,GAAG,GAAG;4BAC1C,MAAM,qBAEL,CAFK,IAAIE,MACR,CAAC,mBAAA,wBAA2C,EAAEmC,WAAWzC,YAAY,CAACI,UAAU,CAAC,IAAI,CAAC,GADlF,qBAAA;uCAAA,SAAA;4CAAA;8CAAA;4BAEN,IAAA;wBACF,IAAA;wBAEAJ,eAAe;4BACbI,UAAAA,EAAYqC,WAAWzC,GAAAA,CAAAA,QAAY,CAACI,UAAU,KAAA,mBAAA;4BAC9CiC,GAAAA,KAAQI,EAAAA,2BAAAA,WAAWzC,YAAY,qBAAvByC,yBAAyBJ,MAAM,KAAI7J,WAAWqG,UAAU;wBAClE,QAAA;oBACF,OAGK,CAAA;wBACHmB,eAAe;4BAAEI,QAAAA,IAAY9L,CAAAA,EAAAA;4BAAgB+N,QAAQ9H,uCAAAA;wBAAU,SAAA;oBACjE,eAAA;wBACF,YAAA;wBACF,QAAA;oBAEAkI,GAAWzC,YAAY,GAAGA;gBAGxB,OAAOvF,IAAAA,CAAAA,OAAAA,cAA0B,YACjCiI,CAAAA,8BAAAA,WAAYnN,IAAI,MAAKrB,gBAAgBsB,QAAQ,IAC7CkN,WAAWzB,WAAW,EACtB;oBAeayB,IAAAA,CAAAA,IAAAA,SAAAA,CAAAA,kBAAAA;wBAdb,eAAA,gDAAuE;4BACvE,YAAA,8CAAsE;4BACtE,QAAA,kDAAsE;wBAEtE,4DAAoE;oBACpE,mEAAuE;gBACvE,OAAA,IAAA,WAAA,YAAA,EAAA,oCAAwE;oBACxE,kEAAsE,MAAA;oBACtE,oBAAA,8CAAsE;oBACtE,IAAA,OAAA,WAAA,YAAA,CAAA,UAAA,KAAA,EAAwD,QAAA;wBACpDE,IAAAA,CAAS,CAAC7O,0BAA0B;wBAExC,IAAA,WAAA,YAAA,CAAA,UAAA,GAAA,GAAA,kBAAsE;4BACtE,MAAA,OAAA,cAAA,CAAA,IAAA,EAA8C,IAAA,CAAA,2CAAA,EAAA,WAAA,YAAA,CAAA,UAAA,CAAA,IAAA,CAAA,GAAA,qBAAA;gCACjC2O,OAAAA,cAAAA,WAAWxJ,OAAO,qBAAlBwJ,oBAAoB,CAACnO,uBAAuB;gCACrD0C,GAAe8B,SAASiK,QAAQ,OAAOA,SAAS,UAAU;gCACxDJ,CAAS,CAACrO,YAAAA,YAAwByO;4BACxC;wBAEMC,eAAiBP,WAAWzB,WAAW,CAAC3E,GAAG,CAAC7B;wBAC9CwI,eAAmB1I,WAAW;4BAChC,IAAY,QAAA,WAAA,YAAA,CAAA,UAAA;4BACL9F,QAAAA,CAAAA,CAAAA,MAAiB,qBAAA,WAAA,YAAA,KAAA,OAAA,KAAA,IAAA,yBAAA,MAAA,KAAA,WAAA,UAAA;wBACtB8B;wBACAC,GAAAA;wBACA0M,MAAM,SAAA;4BACNC,WAAe3K,CAAAA,uJAAAA,CAAAA,UAAW2K,OAAAA,SAAa;4BACvCC,QAAAA,KAAiB5K,WAAW4K,eAAe;wBAC3CrD,QAAQ1L,aAAakO,UAAU,CAACU;wBAChCjD,cAAcyC,WAAWzC,YAAY;oBACvC;gBACF;gBAEA,OAAA,YAAA,GAAA,mDAAyE;gBACzE,OAAA,0BAAA,YAAA,CAAA,cAAA,OAAA,KAAA,CAAyE,GAAA,WAAA,IAAA,MAAA,2KAAA,CAAA,kBAAA,CAAA,QAAA,IAAA,WAAA,WAAA,EAAA;gBACzE,IAAA,kEAAsE;gBACtE,qEAAqE,EAAA;gBACrE,oEAAoE,EAAA;gBACpE,gCAAgC,sCAAA;gBAChCxJ,IAAIY,UAAU,GAAG,mDAAA;gBACjB,OAAO3C,iBAAiB,+CAAA;oBACtB8B,oEAAAA;oBACAC,kEAAAA;oBACA0M,MAAM,4DAAA;oBACNC,eAAe3K,WAAW2K,aAAa,aAAA;oBACvCC,SAAAA,CAAAA,OAAiB5K,WAAW4K,qKAA5BA,CAAAA,2BAA4BA,EAAAA,OAAe;oBAC3CrD,QAAQ1L,aAAakO,UAAU,CAAC,kCAAA;oBAChCvC,cAAcyC,WAAWzC,YAAY,KAAA;gBACvC,MAAA,OAAA,CAAA,uBAAA,WAAA,OAAA,KAAA,OAAA,KAAA,IAAA,oBAAA,CAAA,uJAAA,CAAA,yBAAA,CAAA;gBACF,IAAA,eAAA,SAAA,QAAA,OAAA,SAAA,UAAA;oBAEA,IAAA,SAAA,CAAA,uJAAA,CAAA,yBAAA,EAAA,2BAAyE;gBACzE,0BAA8B;gBAC9B,EAAMqD,IAAAA,WAAetQ,MAAAA,SAAewD,EAAAA,GAAK,QAAA,CAAA,GAAA,CAAA;gBACrC8M,IAAAA,UAAc,SAAA,WAAA;oBAChB,EAAMC,UAAAA,CAAW,MAAMD,aACrB;oBACE,GAAGZ,IAAAA,CAAAA,GAAAA,EAAU,8JAAVA,CAAAA,mBAAU,EAAA;wBACb,sCAA0C;wBAC1C,oCAAwC;wBACxC7B,GAAO,GAAA;wBACL,GAAG6B,WAAW7B,CAAAA,IAAK,OAAA,aAAA;wBACnBrL,MAAM,WAAA,WAAA,eAAA;wBACR,QAAA,iKAAA,CAAA,UAAA,CAAA,UAAA,CAAA;wBAEF,cAAA,WAAA,YAAA;oBACEsH,KAAK9J,eAAewD,KAAK;gBAC3B;gBAEF,IAAI+M,UAAU,2DAAA;oBACZ,0CAA0C,2BAAA;oBAC1C,OAAO,2DAAA;gBACT,qEAAA;gBACF,oEAAA;gBAEA,gCAAA,gCAAoE;gBACpE,IAAA,QAAgB,EAAA,GAAA;gBACZT,OAAAA,CAAAA,GAAAA,QAAevI,wJAAfuI,CAAAA,mBAAevI,EAAAA,SAAkB;oBACnC,EAAM,qBAEL,CAFK,IAAIgG,MACR,yEADI,qBAAA;2BAAA;gCAAA;kCAAA,CAAA,WAAA,aAAA;oBAEN,iBAAA,WAAA,eAAA;oBACF,QAAA,iKAAA,CAAA,UAAA,CAAA,UAAA,CAAA;oBAEIoC,OAAWxJ,OAAO,EAAE,SAAA,YAAA;gBACtB,MAAMA,UAAU;oBAAE,GAAGwJ,WAAWxJ,OAAO;gBAAC,qEAAA;gBAExC,IAAI,CAACjC,eAAe,CAAC8B,KAAAA,EAAO;oBAC1B,OAAOG,MAAAA,CAAAA,GAAAA,gKAAAA,CAAAA,CAAO,CAAC3E,eAAAA,EAAAA,KAAAA,KAAuB;gBACxC,cAAA;gBAEA,KAAK,CAAA,GAAI,CAACgP,KAAK3C,EAAAA,IAAM,EAAA,EAAI4C,OAAOC,IAAAA,GAAO,CAACvK,SAAU;oBAChD,GAAA,CAAI,OAAO0H,EAAAA,QAAU,aAAa;oBAElC,IAAI8C,MAAMC,OAAO,CAAC/C,QAAQ,gBAAA;wBACxB,KAAK,MAAMgD,KAAKhD,MAAO,cAAA;4BACrBpK,IAAIqN,YAAY,CAACN,KAAKK;wBACxB,GAAA,WAAA,KAAA;wBACF,GAAO,GAAA,CAAI,OAAOhD,UAAU,UAAU;wBACpCA,QAAQA,MAAMkD,QAAQ;wBACtBtN,IAAIqN,YAAY,CAACN,KAAK3C;oBACxB,KAAA,CAAA,GAAA,gKAAA,CAAA,EAAO,eAAA,EAAA,KAAA;wBACLpK,IAAIqN,YAAY,CAACN,KAAK3C;oBACxB,UAAA;oBACF,0CAAA;oBACF,OAAA;gBAEA,kEAAsE;YACtE,8CAA8C;YAC9C,MAAMoC,QAAON,sBAAAA,WAAWxJ,OAAO,cAAA,OAAlBwJ,mBAAoB,CAACnO,uBAAuB;YACzD,IAAI0C,YAAAA,GAAe8B,SAASiK,QAAQ,OAAOA,SAAS,UAAU;gBAC5DxM,IAAIoM,SAAS,CAACrO,CAAAA,kBAAAA,KAAwByO;gBACxC,MAAA,OAAA,cAAA,CAAA,IAAA,MAAA,yEAAA,qBAAA;oBAEA,OAAA,2DAA0E;oBAC1E,YAAA,sDAA0E;oBAC1E,cAAA,cAAoC;gBAChCN,WAAW1B,MAAM,IAAK,CAAA,CAAC1H,gBAAgB,CAACS,iBAAgB,GAAI;gBAC9DvD,IAAIY,UAAU,GAAGsL,WAAW1B,MAAM;YACpC,IAAA,WAAA,OAAA,EAAA;gBAEA,MAAA,UAAA,4EAAgG;oBAE7F/J,GAAAA,SACDyL,EAAAA,OAAAA,EAAW1B,MAAM,IACjB5L,kBAAkB,CAACsN,WAAW1B,MAAM,CAAC,IACrC1H,cACA;gBACA9C,IAAIY,UAAU,GAAG;gBACnB,IAAA,CAAA,eAAA,CAAA,OAAA;oBAEA,OAAA,OAAA,CAAA,uJAAA,CAAA,eAAsC,UAAA,CAAA;gBAClCyL,aAAa;gBACfrM,IAAIoM,CAAAA,IAAAA,CAAAA,GAAS,CAAC7O,CAAAA,MAAAA,IAAAA,OAAAA,OAAAA,CAA0B,SAAA;oBAC1C,IAAA,OAAA,UAAA,aAAA;oBAEA,IAAA,MAAA,OAAA,CAAA,QAAA,yBAA2D;wBAC3D,KAAA,MAAA,KAAA,MAAA,kCAAoE;4BACpE,IAAA,YAAA,CAAA,KAAA,oCAA0E;wBAC1E,mBAA+B;oBAC3BuF,OAAAA,IAAAA,CAAgB,CAAClB,KAAAA,QAAa,EAAA,UAAA;wBAChC,QAAA,MAAA,QAAA,gCAA8D;wBAC1D,GAAOsK,CAAAA,UAAW5B,EAAAA,CAAAA,IAAO,CAAA,IAAK,aAAa;oBAC7C,IAAI4B,GAAAA,QAAW3F,SAAS,EAAE;wBACxB,IAAA,EAAM,UAAA,CAAA,KAAA,KAA0D,CAA1D,IAAIuD,MAAM,kDAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAyD,0DAAA;oBACjE,sCAAA;oBAEA,KAAA,CAAA,CAAO7L,iBAAiB,IAAA,WAAA,OAAA,KAAA,OAAA,KAAA,IAAA,mBAAA,CAAA,uJAAA,CAAA,yBAAA,CAAA;wBACtB8B,OAAAA,SAAAA,QAAAA,OAAAA,SAAAA,UAAAA;wBACAC,KAAAA,CAAAA,uJAAAA,CAAAA,yBAAAA,EAAAA;wBACA0M,MAAM;wBACNC,eAAe3K,WAAW2K,aAAa,uBAAA;wBACvCC,iBAAiB5K,WAAW4K,eAAe,mBAAA;wBAC3CrD,QAAQ2C,WAAW7B,IAAI,CAAA;wBACvB,GAAA,MAAA,IAAA,CAAA,CAAA,gBAAA,CAAA,iBAAA,GAAA,MAA0D;wBAC1D,MAAA,GAAA,WAAA,MAAA,iCAA2D;wBAC3D,+DAA+D;wBAC/D,mBAAmB,iEAAA;wBACnB,QAAA,WAAA,MAAA,IAAA,yLAAA,CAAA,qBAAA,CAAA,WAAA,MAAA,CAAA,IAAA,SAA+E,KAAA;wBAC/Eb,MAAAA,GAAAA,KAAcxF,sBACV;4BAAE4F,YAAY;4BAAGiC,QAAQ9H,cAAAA;wBAAU,IACnCkI,CAAAA,UAAWzC,YAAY;oBAC7B,SAAA,CAAA,uLAAA,CAAA,2BAAA,EAAA;gBACF;gBAEA,uDAAA,eAAsE;gBACtE,QAAQ,wDAAA;gBACR,OAAOvL,iBAAiB,8CAAA;oBACtB8B,uBAAAA;oBACAC,YAAAA,CAAAA,aAAAA;oBACA0M,MAAM,oDAAA;oBACNC,OAAAA,QAAe3K,GAAAA,OAAAA,CAAW2K,IAAAA,SAAa,IAAA;oBACvCC,IAAAA,WAAAA,EAAiB5K,OAAAA,EAAAA,EAAW4K,eAAe;wBAC3CrD,IAAQ1L,EAAAA,OAAAA,IAAakO,UAAU,CAACG,IAAAA,MAAAA,CAAW5B,OAAO,0CAAA,qBAAA;4BAClDd,MAAcyC,CAAAA,UAAWzC,YAAY;4BACvC,YAAA;4BACF,cAAA;wBAEA,uBAAmC;oBAC/B+D,GAAOrB,WAAW7B,IAAI;oBAE1B,OAAA,CAAA,GAAA,gKAAA,CAAA,mBAAA,EAAA,qCAAqE;wBACrE,0DAAsE;wBACtE,wCAAoD;wBAC/CgC,MAAAA,EAAe5L,aAAa;wBACxBxC,eAAAA,CAAiB,UAAA,aAAA;wBACtB8B,iBAAAA,WAAAA,eAAAA;wBACAC,QAAAA,WAAAA,IAAAA;wBACA0M,EAAM,wDAAA;wBACNC,WAAe3K,WAAW2K,aAAa,wBAAA;wBACvCC,aAAiB5K,WAAW4K,eAAe,wBAAA;wBAC3CrD,IAAQgE,eAAAA;wBACR/D,UAAcyC,WAAWzC,YAAY,8CAAA;wBACvC,cAAA,sBAAA;4BACF,YAAA;4BAEA,QAAA,8CAAsE;wBACtE,IAAA,WAAA,YAAA,gCAAuE;oBACvE,8DAAsE;gBACtE,wBAA4B;gBACxB7F,sBAAsBC,wBAAwB,wBAAA;gBAChD,QAAA,2DAAmE;gBACnE,OAAA,CAAA,GAAA,gKAAA,CAAA,mBAAA,EAAA,2BAAmD;oBACnD2J,CAAKC,KAAK,CACR,IAAIC,eAAe;oBACjBC,OAAMC,UAAU;wBACdA,EAAAA,SAAWC,OAAO,CAAC5P,aAAa6P,MAAM,CAACC,aAAa;wBACpDH,WAAWI,KAAK,MAAA,aAAA;oBAClB,iBAAA,WAAA,eAAA;oBACF,QAAA,iKAAA,CAAA,UAAA,CAAA,UAAA,CAAA,WAAA,OAAA;oBAGF,GAAO9P,WAAAA,MAAiB,KAAA,YAAA;oBACtB8B;oBACAC;oBACA0M,MAAM,qBAAA;oBACNC,GAAAA,WAAAA,CAAe3K,GAAAA,QAAW2K,aAAa;oBACvCC,iBAAiB5K,WAAW4K,eAAe,kBAAA;oBAC3CrD,QAAQgE,sDAAAA;oBACR/D,cAAc,8BAAA;wBAAEI,QAAAA,IAAY,SAAA;6LAAGiC,QAAQ9H,UAAAA,EAAAA;oBAAU;oBACnD;oBACF,MAAA;oBAEA,eAAA,WAAA,aAAA,0BAAyE;oBACzE,iBAAA,WAAA,eAAA,qBAAwE;oBACxE,QAAA,GAAmB;oBACbiK,YAAc,EAAA,EAAIC,SAAAA,YAAAA;gBACxBV,CAAKC,KAAK,CAACQ,YAAYE,QAAQ;YAE/B,wEAAwE;YACxE,sEAAA,EAAwE;YACxE,uEAAA,EAAyE;YACzE5H,SAAS,6DAAA;gBACPvB,wBAAAA;gBACAwB,WAAW2F,WAAW3F,SAAS,eAAA;gBAC/B,mEAAA,GAAsE;gBACtE,YAAY,uCAAA;gBACZC,KAAAA,KAAAA,CAAAA,IAAAA,MAAqB,SAAA;oBAEf,OAAO+C,UAAAA;wBAKPA,WAAAA,OAAAA,CAAAA,mLAAAA,CAAAA,eAAAA,CAAAA,MAAAA,CAAAA,aAAAA;wBAJCA,KAAQ,MAAA,KAAA;oBACX,MAAM,qBAAwD,CAAxD,IAAIO,MAAM,gDAAV,qBAAA;+BAAA;oCAAA,2KAAA,EAAA;sCAAA;oBAAuD;oBAC/D,MAAA;oBAEIP,EAAAA,aAAAA,GAAAA,OAAOa,CAAAA,IAAK,SAAA,YAAZb,cAAcxK,IAAI,MAAKrB,gBAAgBsB,QAAQ,EAAE;wBAELuK,aAAAA,WAAAA,eAAAA;oBAD9C,MAAM,EAAA,mBAEL,CAFK,IAAIO,MACR,CAAC,yCAAyC,GAAEP,iBAAAA,OAAOa,KAAK,qBAAZb,eAAcxK,IAAI,EAAE,GAD5D,qBAAA;+BAAA,GAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,6CAA6C;gBAC7C,MAAMwK,OAAOa,KAAK,CAACC,IAAI,CAAC+D,MAAM,CAACJ,YAAYK,QAAQ,kBAAA;YACrD,GACCC,KAAK,CAAC,CAACjF,8DAAAA;gBACN,eAAA,kDAAiE;gBACjE,EAAA,cAAA,IAAA,sCAA0D;gBAC1D2E,CAAAA,KAAAA,CAAAA,KAAYK,OAAAA,CAAQ,CAACE,KAAK,CAAClF,KAAKiF,KAAK,CAAC,CAACE;oBACrCxI,QAAQiD,KAAK,CAAC,8BAA8BuF,oBAAAA;gBAC9C,oEAAA;YACF,yEAAA;YAEF,OAAOvQ,EAAAA,eAAiB;gBACtB8B;gBACAC,WAAAA,WAAAA,SAAAA;gBACA0M,MAAM,gEAAA;gBACNC,YAAAA,GAAe3K,WAAW2K,aAAa;gBACvCC,iBAAiB5K,IAAAA,OAAW4K,eAAe;gBAC3CrD,GAAAA,CAAAA,IAAQgE,GAAAA;gBACR,IAAA,mEAAuE;gBACvE,IAAA,CAAA,QAAA,2DAAwE;oBACxE,MAAA,OAAA,cAAA,CAAA,IAAA,CAAqC,KAAA,gDAAA,qBAAA;wBACrC/D,MAAc,CAAA;wBAAEI,QAAY,IAAA;wBAAGiC,IAAQ9H,UAAAA;oBAAU;gBACnD;gBACF,IAAA,CAAA,CAAA,gBAAA,OAAA,KAAA,KAAA,OAAA,KAAA,IAAA,cAAA,IAAA,MAAA,2KAAA,CAAA,kBAAA,CAAA,QAAA,EAAA;oBAEA,IAAA,oCAAoD;oBACpD,MAAA,OAAA,cAAA,CAAA,IAAA,MAAA,CAAA,MAAyD,mCAAA,EAAA,CAAA,iBAAA,OAAA,KAAA,KAAA,OAAA,KAAA,IAAA,eAAA,IAAA,EAAA,GAAA,qBAAA;wBACzC,OAAA;wBACR0H,SAAe7G,GAAAA;wBAChB,cAAA;oBACE,KAAMD,OAAO8J,qBAAqB,CAAC1O,IAAI2C,OAAO,EAAE,IACrDiC,OAAO+J,KAAK,CACVlS,eAAeuJ,aAAa,EAC5B;oBACE4I,UAAU,GAAGjK,OAAO,CAAC,EAAE3E,IAAIsG,GAAG,EAAE;oBAChCtH,MAAMzC,SAASsS,MAAM,oBAAA;oBACrBC,EAAAA,OAAAA,GAAY,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,YAAA,QAAA;wBACV,eAAenK;wBACf,eAAe3E,IAAIsG,GAAG,mCAAA;oBACxB,sDAAA;gBACF,GACAoF,SAAAA,QAAAA,CAAAA,KAAAA,CAAAA,KAAAA,KAAAA,CAAAA,CAAAA;oBAGN,QAAA,KAAA,CAAA,8BAAA;gBACOpC,EAAK;YACZ,8CAAkD;YAC9C,CAACzE,MAAAA,CAAAA,GAAAA,IAAc,CAAEyE,CAAAA,0JAAhBzE,CAAAA,mBAAgByE,EAAAA,QAAenL,eAAc,GAAI;gBACpD,EAAMW,YAAYuK,cAAc,CAC9BrJ,KACAsJ,KACA;gBACEyF,YAAY;gBACZC,MAAAA,KAAW5O;gBACX6O,WAAW,IAAA,WAAA,aAAA;gBACXC,iBAAAA,CAAkB7S,UAAAA,UAAoB,KAAA;oBACpC6K,IAAAA,UAAc1E;oBACdL,mEAAAA;gBACF,wEAAA;gBAEFH,qCAAAA;gBAEJ,cAAA;oBAEA,YAAA,2BAAmD;oBAC7CsH,QAAAA;gBACR;YACF", "ignoreList": [0], "debugId": null}}]}