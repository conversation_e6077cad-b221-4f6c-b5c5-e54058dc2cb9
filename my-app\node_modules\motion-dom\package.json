{"name": "motion-dom", "version": "12.23.12", "author": "<PERSON>", "license": "MIT", "repository": "https://github.com/motiondivision/motion", "main": "./dist/cjs/index.js", "types": "./dist/index.d.ts", "module": "./dist/es/index.mjs", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "require": "./dist/cjs/index.js", "import": "./dist/es/index.mjs", "default": "./dist/cjs/index.js"}}, "dependencies": {"motion-utils": "^12.23.6"}, "scripts": {"clean": "rm -rf types dist lib", "build": "yarn clean && tsc -p . && rollup -c && node ./scripts/check-bundle.js", "dev": "concurrently -c blue,red -n tsc,rollup --kill-others \"tsc --watch -p . --preserveWatchOutput\" \"rollup --config --watch --no-watch.clearScreen\"", "test": "jest --config jest.config.json --max-workers=2", "measure": "rollup -c ./rollup.size.config.mjs"}, "bundlesize": [{"path": "./dist/size-rollup-style-effect.js", "maxSize": "2.9 kB"}, {"path": "./dist/size-rollup-motion-value.js", "maxSize": "1.8 kB"}], "gitHead": "e0f7e07570e281b8932c897afb5f6a348c7f97de"}