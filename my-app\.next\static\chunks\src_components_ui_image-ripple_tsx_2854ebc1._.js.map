{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/image-ripple.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useEffect, useRef, useState } from \"react\"\nimport { OrthographicCamera, useFBO, useTexture } from \"@react-three/drei\"\nimport { Canvas, useFrame, useThree } from \"@react-three/fiber\"\nimport * as THREE from \"three\"\n\nexport default function Scene() {\n  const device = useDimension()\n\n  if (!device.width || !device.height) {\n    return null\n  }\n\n  const frustumSize = device.height\n  const aspect = device.width / device.height\n\n  return (\n    <div className=\"relative flex h-screen w-full items-center justify-center \">\n      <Canvas>\n        <OrthographicCamera\n          makeDefault\n          args={[\n            (frustumSize * aspect) / -2,\n            (frustumSize * aspect) / 2,\n            frustumSize / 2,\n            frustumSize / -2,\n            -1000,\n            1000,\n          ]}\n          position={[0, 0, 2]}\n        />\n        <Model />\n      </Canvas>\n    </div>\n  )\n}\n\nfunction Model() {\n  const { viewport } = useThree()\n  const texture = useTexture(\"/brush.png\")\n  const meshRefs = useRef<(THREE.Mesh | null)[]>([])\n  const [meshes, setMeshes] = useState<JSX.Element[]>([])\n  const mouse = useMouse()\n  const device = useDimension()\n  const [prevMouse, setPrevMouse] = useState({ x: 0, y: 0 })\n  const [currentWave, setCurrentWave] = useState(0)\n  const { gl, camera } = useThree()\n\n  const scene = new THREE.Scene()\n  const max = 100\n\n  const uniforms = useRef({\n    uDisplacement: { value: new THREE.Texture() },\n    uTexture: { value: new THREE.Texture() },\n    winResolution: {\n      value: new THREE.Vector2(0, 0),\n    },\n  })\n\n  const fboBase = useFBO(device.width, device.height)\n  const fboTexture = useFBO(device.width, device.height)\n\n  const { scene: imageScene, camera: imageCamera } = Images(\n    new THREE.Vector2(viewport.width, viewport.height)\n  )\n\n  useEffect(() => {\n    const generatedMeshes = Array.from({ length: max }).map((_, i) => (\n      <mesh\n        key={i}\n        position={[0, 0, 0]}\n        ref={(el) => {\n          meshRefs.current[i] = el\n        }}\n        rotation={[0, 0, Math.random()]}\n        visible={false}\n      >\n        <planeGeometry args={[60, 60, 1, 1]} />\n        <meshBasicMaterial transparent={true} map={texture} />\n      </mesh>\n    ))\n    setMeshes(generatedMeshes)\n  }, [texture])\n\n  function setNewWave(x: number, y: number, currentWave: number) {\n    const mesh = meshRefs.current[currentWave]\n    if (mesh) {\n      mesh.position.x = x\n      mesh.position.y = y\n      mesh.visible = true\n      ;(mesh.material as THREE.Material).opacity = 1\n      mesh.scale.x = 1.75\n      mesh.scale.y = 1.75\n    }\n  }\n\n  function trackMousePos(x: number, y: number) {\n    if (Math.abs(x - prevMouse.x) > 0.1 || Math.abs(y - prevMouse.y) > 0.1) {\n      setCurrentWave((currentWave + 1) % max)\n      setNewWave(x, y, currentWave)\n    }\n    setPrevMouse({ x: x, y: y })\n  }\n\n  useFrame(({ gl, scene: finalScene }) => {\n    const x = mouse.x - device.width / 1.65\n    const y = -mouse.y + device.height / 1.5\n    trackMousePos(x, y)\n    meshRefs.current.forEach((mesh) => {\n      if (mesh && mesh.visible) {\n        mesh.rotation.z += 0.025\n        ;(mesh.material as THREE.MeshBasicMaterial).opacity *= 0.95\n        mesh.scale.x = 0.98 * mesh.scale.x + 0.155\n        mesh.scale.y = 0.98 * mesh.scale.y + 0.155\n      }\n    })\n\n    if (device.width > 0 && device.height > 0) {\n      // uniforms.current.uTexture.value = imageTexture;\n\n      // Render to base texture with meshes\n      gl.setRenderTarget(fboBase)\n      gl.clear()\n      meshRefs.current.forEach((mesh) => {\n        if (mesh && mesh.visible) {\n          scene.add(mesh)\n        }\n      })\n      gl.render(scene, camera)\n      meshRefs.current.forEach((mesh) => {\n        if (mesh && mesh.visible) {\n          scene.remove(mesh)\n        }\n      })\n      uniforms.current.uTexture.value = fboTexture.texture\n\n      gl.setRenderTarget(fboTexture)\n      gl.render(imageScene, imageCamera)\n      uniforms.current.uDisplacement.value = fboBase.texture\n\n      gl.setRenderTarget(null)\n      gl.render(finalScene, camera)\n\n      // Render the scene with updated displacement\n      // gl.setRenderTarget(fboTexture);\n      // gl.clear();\n      // gl.render(scene, camera);\n      // uniforms.current.uTexture.value = fboTexture.texture;\n      // gl.setRenderTarget(null);\n\n      uniforms.current.winResolution.value = new THREE.Vector2(\n        device.width,\n        device.height\n      ).multiplyScalar(device.pixelRatio)\n    }\n  }, 1)\n\n  function Images(viewport: THREE.Vector2) {\n    const scene = new THREE.Scene()\n    const camera = new THREE.OrthographicCamera(\n      viewport.width / -2,\n      viewport.width / 2,\n      viewport.height / 2,\n      viewport.height / -2,\n      -1000,\n      1000\n    )\n    camera.position.z = 2\n    scene.add(camera)\n    const geometry = new THREE.PlaneGeometry(1, 1)\n    const group = new THREE.Group()\n\n    const texture1 = useTexture(\"/picture9.jpeg\")\n    const material1 = new THREE.MeshBasicMaterial({ map: texture1 })\n    const image1 = new THREE.Mesh(geometry, material1)\n    image1.position.x = -0.3 * viewport.width\n    image1.position.y = 0\n    image1.position.z = 1\n    image1.scale.x = 1080 / 4\n    image1.scale.y = 1920 / 4\n    group.add(image1)\n\n    const texture2 = useTexture(\"/picture1.jpeg\")\n    const material2 = new THREE.MeshBasicMaterial({ map: texture2 })\n    const image2 = new THREE.Mesh(geometry, material2)\n    image2.position.x = -0.001 * viewport.width\n    image2.position.y = 0\n    image2.position.z = 1\n    image2.scale.x = 1080 / 4\n    image2.scale.y = 1920 / 4\n    group.add(image2)\n\n    // const texture3 = useTexture('/picture3.jpeg');\n    // const material3 = new THREE.MeshBasicMaterial({ map: texture3 });\n    // const image3 = new THREE.Mesh(geometry, material3);\n    // image3.position.x = 0.25 * viewport.width;\n    // image3.position.y = 0;\n    // image3.position.z = 1;\n    // image3.scale.x = viewport.width / 5;\n    // image3.scale.y = viewport.width / 4;\n    // group.add(image3);\n\n    scene.add(group)\n    return { scene, camera }\n  }\n\n  return (\n    <group>\n      {meshes}\n      {/* <Images /> */}\n      <mesh>\n        <planeGeometry args={[device.width, device.height, 1, 1]} />\n        <shaderMaterial\n          // args={[device.width, device.height, 1]}\n          vertexShader={vertex}\n          fragmentShader={fragment}\n          transparent={true}\n          uniforms={uniforms.current}\n        ></shaderMaterial>\n      </mesh>\n    </group>\n  )\n}\n\nfunction useMouse() {\n  const [mouse, setMouse] = React.useState({ x: 0, y: 0, pixelRatio: 0 })\n\n  const mouseMove = (e: { clientX: any; clientY: any }) => {\n    const { clientX, clientY } = e\n    setMouse({\n      x: clientX,\n      y: clientY,\n      pixelRatio: Math.min(window.devicePixelRatio, 2),\n    })\n  }\n\n  React.useEffect(() => {\n    window.addEventListener(\"mousemove\", mouseMove)\n    return () => {\n      window.removeEventListener(\"mousemove\", mouseMove)\n    }\n  }, [])\n\n  return mouse\n}\n\nfunction useDimension() {\n  const [dimension, setDimension] = React.useState({\n    width: 0,\n    height: 0,\n    pixelRatio: 1,\n  })\n\n  React.useEffect(() => {\n    if (typeof window !== \"undefined\") {\n      const resize = () => {\n        setDimension({\n          width: window.innerWidth,\n          height: window.innerHeight,\n          pixelRatio: window.devicePixelRatio,\n        })\n      }\n\n      resize()\n\n      window.addEventListener(\"resize\", resize)\n\n      return () => window.removeEventListener(\"resize\", resize)\n    }\n  }, [])\n\n  return dimension\n}\n\nconst fragment = `\nuniform sampler2D uTexture;\nuniform sampler2D uDisplacement;\nuniform vec4 winResolution;\nvarying vec2 vUv;\nfloat PI = 3.141592653589793238;\n\nvoid main() {\n  vec2 vUvScreen = gl_FragCoord.xy / winResolution.xy;\n\n  vec4 displacement = texture2D(uDisplacement, vUvScreen);\n  float theta = displacement.r*2.0*PI;\n\n  vec2 dir = vec2(sin(theta),cos(theta));\n  vec2 uv = vUvScreen + dir*displacement.r*0.075;\n  vec4 color = texture2D(uTexture,uv);\n\n  gl_FragColor = color;\n}\n`\n\nconst vertex = `\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS;IAEf,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,MAAM,EAAE;QACnC,OAAO;IACT;IAEA,MAAM,cAAc,OAAO,MAAM;IACjC,MAAM,SAAS,OAAO,KAAK,GAAG,OAAO,MAAM;IAE3C,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sMAAA,CAAA,SAAM;;8BACL,6LAAC,yKAAA,CAAA,qBAAkB;oBACjB,WAAW;oBACX,MAAM;wBACH,cAAc,SAAU,CAAC;wBACzB,cAAc,SAAU;wBACzB,cAAc;wBACd,cAAc,CAAC;wBACf,CAAC;wBACD;qBACD;oBACD,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;;;;;;8BAErB,6LAAC;;;;;;;;;;;;;;;;AAIT;GA7BwB;;QACP;;;KADO;AA+BxB,SAAS;;;IACP,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;IAC5B,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,QAAQ;IACd,MAAM,SAAS;IACf,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACxD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;IAE9B,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAW;IAC7B,MAAM,MAAM;IAEZ,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QACtB,eAAe;YAAE,OAAO,IAAI,kJAAA,CAAA,UAAa;QAAG;QAC5C,UAAU;YAAE,OAAO,IAAI,kJAAA,CAAA,UAAa;QAAG;QACvC,eAAe;YACb,OAAO,IAAI,kJAAA,CAAA,UAAa,CAAC,GAAG;QAC9B;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE,OAAO,KAAK,EAAE,OAAO,MAAM;IAClD,MAAM,aAAa,CAAA,GAAA,0JAAA,CAAA,SAAM,AAAD,EAAE,OAAO,KAAK,EAAE,OAAO,MAAM;IAErD,MAAM,EAAE,OAAO,UAAU,EAAE,QAAQ,WAAW,EAAE,GAAG,OACjD,IAAI,kJAAA,CAAA,UAAa,CAAC,SAAS,KAAK,EAAE,SAAS,MAAM;IAGnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM,kBAAkB,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAI,GAAG,GAAG;mDAAC,CAAC,GAAG,kBAC1D,6LAAC;wBAEC,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBACnB,GAAG;+DAAE,CAAC;gCACJ,SAAS,OAAO,CAAC,EAAE,GAAG;4BACxB;;wBACA,UAAU;4BAAC;4BAAG;4BAAG,KAAK,MAAM;yBAAG;wBAC/B,SAAS;;0CAET,6LAAC;gCAAc,MAAM;oCAAC;oCAAI;oCAAI;oCAAG;iCAAE;;;;;;0CACnC,6LAAC;gCAAkB,aAAa;gCAAM,KAAK;;;;;;;uBATtC;;;;;;YAYT,UAAU;QACZ;0BAAG;QAAC;KAAQ;IAEZ,SAAS,WAAW,CAAS,EAAE,CAAS,EAAE,WAAmB;QAC3D,MAAM,OAAO,SAAS,OAAO,CAAC,YAAY;QAC1C,IAAI,MAAM;YACR,KAAK,QAAQ,CAAC,CAAC,GAAG;YAClB,KAAK,QAAQ,CAAC,CAAC,GAAG;YAClB,KAAK,OAAO,GAAG;YACb,KAAK,QAAQ,CAAoB,OAAO,GAAG;YAC7C,KAAK,KAAK,CAAC,CAAC,GAAG;YACf,KAAK,KAAK,CAAC,CAAC,GAAG;QACjB;IACF;IAEA,SAAS,cAAc,CAAS,EAAE,CAAS;QACzC,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,OAAO,KAAK,GAAG,CAAC,IAAI,UAAU,CAAC,IAAI,KAAK;YACtE,eAAe,CAAC,cAAc,CAAC,IAAI;YACnC,WAAW,GAAG,GAAG;QACnB;QACA,aAAa;YAAE,GAAG;YAAG,GAAG;QAAE;IAC5B;IAEA,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;0BAAE;gBAAC,EAAE,EAAE,EAAE,OAAO,UAAU,EAAE;YACjC,MAAM,IAAI,MAAM,CAAC,GAAG,OAAO,KAAK,GAAG;YACnC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,OAAO,MAAM,GAAG;YACrC,cAAc,GAAG;YACjB,SAAS,OAAO,CAAC,OAAO;kCAAC,CAAC;oBACxB,IAAI,QAAQ,KAAK,OAAO,EAAE;wBACxB,KAAK,QAAQ,CAAC,CAAC,IAAI;wBACjB,KAAK,QAAQ,CAA6B,OAAO,IAAI;wBACvD,KAAK,KAAK,CAAC,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;wBACrC,KAAK,KAAK,CAAC,CAAC,GAAG,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;oBACvC;gBACF;;YAEA,IAAI,OAAO,KAAK,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;gBACzC,kDAAkD;gBAElD,qCAAqC;gBACrC,GAAG,eAAe,CAAC;gBACnB,GAAG,KAAK;gBACR,SAAS,OAAO,CAAC,OAAO;sCAAC,CAAC;wBACxB,IAAI,QAAQ,KAAK,OAAO,EAAE;4BACxB,MAAM,GAAG,CAAC;wBACZ;oBACF;;gBACA,GAAG,MAAM,CAAC,OAAO;gBACjB,SAAS,OAAO,CAAC,OAAO;sCAAC,CAAC;wBACxB,IAAI,QAAQ,KAAK,OAAO,EAAE;4BACxB,MAAM,MAAM,CAAC;wBACf;oBACF;;gBACA,SAAS,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,WAAW,OAAO;gBAEpD,GAAG,eAAe,CAAC;gBACnB,GAAG,MAAM,CAAC,YAAY;gBACtB,SAAS,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,QAAQ,OAAO;gBAEtD,GAAG,eAAe,CAAC;gBACnB,GAAG,MAAM,CAAC,YAAY;gBAEtB,6CAA6C;gBAC7C,kCAAkC;gBAClC,cAAc;gBACd,4BAA4B;gBAC5B,wDAAwD;gBACxD,4BAA4B;gBAE5B,SAAS,OAAO,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,kJAAA,CAAA,UAAa,CACtD,OAAO,KAAK,EACZ,OAAO,MAAM,EACb,cAAc,CAAC,OAAO,UAAU;YACpC;QACF;yBAAG;IAEH,SAAS,OAAO,QAAuB;;QACrC,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAW;QAC7B,MAAM,SAAS,IAAI,kJAAA,CAAA,qBAAwB,CACzC,SAAS,KAAK,GAAG,CAAC,GAClB,SAAS,KAAK,GAAG,GACjB,SAAS,MAAM,GAAG,GAClB,SAAS,MAAM,GAAG,CAAC,GACnB,CAAC,MACD;QAEF,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,MAAM,GAAG,CAAC;QACV,MAAM,WAAW,IAAI,kJAAA,CAAA,gBAAmB,CAAC,GAAG;QAC5C,MAAM,QAAQ,IAAI,kJAAA,CAAA,QAAW;QAE7B,MAAM,WAAW,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,MAAM,YAAY,IAAI,kJAAA,CAAA,oBAAuB,CAAC;YAAE,KAAK;QAAS;QAC9D,MAAM,SAAS,IAAI,kJAAA,CAAA,OAAU,CAAC,UAAU;QACxC,OAAO,QAAQ,CAAC,CAAC,GAAG,CAAC,MAAM,SAAS,KAAK;QACzC,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,MAAM,GAAG,CAAC;QAEV,MAAM,WAAW,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EAAE;QAC5B,MAAM,YAAY,IAAI,kJAAA,CAAA,oBAAuB,CAAC;YAAE,KAAK;QAAS;QAC9D,MAAM,SAAS,IAAI,kJAAA,CAAA,OAAU,CAAC,UAAU;QACxC,OAAO,QAAQ,CAAC,CAAC,GAAG,CAAC,QAAQ,SAAS,KAAK;QAC3C,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,QAAQ,CAAC,CAAC,GAAG;QACpB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,OAAO,KAAK,CAAC,CAAC,GAAG,OAAO;QACxB,MAAM,GAAG,CAAC;QAEV,iDAAiD;QACjD,oEAAoE;QACpE,sDAAsD;QACtD,6CAA6C;QAC7C,yBAAyB;QACzB,yBAAyB;QACzB,uCAAuC;QACvC,uCAAuC;QACvC,qBAAqB;QAErB,MAAM,GAAG,CAAC;QACV,OAAO;YAAE;YAAO;QAAO;IACzB;OA/CS;;YAeU,8JAAA,CAAA,aAAU;YAUV,8JAAA,CAAA,aAAU;;;IAwB7B,qBACE,6LAAC;;YACE;0BAED,6LAAC;;kCACC,6LAAC;wBAAc,MAAM;4BAAC,OAAO,KAAK;4BAAE,OAAO,MAAM;4BAAE;4BAAG;yBAAE;;;;;;kCACxD,6LAAC;wBACC,0CAA0C;wBAC1C,cAAc;wBACd,gBAAgB;wBAChB,aAAa;wBACb,UAAU,SAAS,OAAO;;;;;;;;;;;;;;;;;;AAKpC;IAzLS;;QACc,kNAAA,CAAA,WAAQ;QACb,8JAAA,CAAA,aAAU;QAGZ;QACC;QAGQ,kNAAA,CAAA,WAAQ;QAaf,0JAAA,CAAA,SAAM;QACH,0JAAA,CAAA,SAAM;QA4CzB,kNAAA,CAAA,WAAQ;;;MAnED;AA2LT,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QAAE,GAAG;QAAG,GAAG;QAAG,YAAY;IAAE;IAErE,MAAM,YAAY,CAAC;QACjB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAC7B,SAAS;YACP,GAAG;YACH,GAAG;YACH,YAAY,KAAK,GAAG,CAAC,OAAO,gBAAgB,EAAE;QAChD;IACF;IAEA,6JAAA,CAAA,UAAK,CAAC,SAAS;8BAAC;YACd,OAAO,gBAAgB,CAAC,aAAa;YACrC;sCAAO;oBACL,OAAO,mBAAmB,CAAC,aAAa;gBAC1C;;QACF;6BAAG,EAAE;IAEL,OAAO;AACT;IApBS;AAsBT,SAAS;;IACP,MAAM,CAAC,WAAW,aAAa,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;QAC/C,OAAO;QACP,QAAQ;QACR,YAAY;IACd;IAEA,6JAAA,CAAA,UAAK,CAAC,SAAS;kCAAC;YACd,wCAAmC;gBACjC,MAAM;qDAAS;wBACb,aAAa;4BACX,OAAO,OAAO,UAAU;4BACxB,QAAQ,OAAO,WAAW;4BAC1B,YAAY,OAAO,gBAAgB;wBACrC;oBACF;;gBAEA;gBAEA,OAAO,gBAAgB,CAAC,UAAU;gBAElC;8CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;YACpD;QACF;iCAAG,EAAE;IAEL,OAAO;AACT;IA1BS;AA4BT,MAAM,WAAY;AAqBlB,MAAM,SAAU", "debugId": null}}]}