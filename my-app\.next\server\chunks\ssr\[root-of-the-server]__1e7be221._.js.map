{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["import { h1 } from 'framer-motion/client'\r\nimport React from 'react'\r\nimport FlipLink from './'\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <h1>\r\n        <FlipLink href=\"https://x.com/guri_who\"><PERSON><PERSON></FlipLink>\r\n    </h1>\r\n  )\r\n}\r\n\r\nexport default HomePage"], "names": [], "mappings": ";;;;;;;;;;;AAIA,MAAM,WAAW;IACf,qBACE,8OAAC;kBACG,cAAA,8OAAC;YAAS,MAAK;sBAAyB;;;;;;;;;;;AAGhD;uCAEe", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nimport HomePage from \"@/components/ui/homePage\";\n\n\nexport default function Home() {\n  return(\n    <>  \n      <HomePage />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAGe,SAAS;IACtB,qBACE;kBACE,cAAA,8OAAC,oIAAA,CAAA,UAAQ;;;;;;AAGf", "debugId": null}}]}