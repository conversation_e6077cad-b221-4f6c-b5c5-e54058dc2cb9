{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\r\n\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <div>\r\n      \r\n    </div>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;;AAIA,MAAM,WAAW;IACf,qBACE,8OAAC;;;;;AAIL;uCAEe", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/app/page.tsx"], "sourcesContent": ["import HomePage from \"@/components/ui/homePage\";\nimport FlipLink from \"@/components/ui/text-effect-flipper\";\nimport React from \"react\";\n\nexport default function page() {\n  return (\n    <div>\n      <h1 className=\"text-white-500 text-7xl\">Hello Everyone</h1>\n      <HomePage/>\n      <FlipLink href=\"https://x.com/guri_who\">I'm <PERSON><PERSON></FlipLink>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;;;;;;;AAIe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC,oIAAA,CAAA,UAAQ;;;;;0BACT,8OAAC;gBAAS,MAAK;0BAAyB;;;;;;;;;;;;AAG9C", "debugId": null}}]}