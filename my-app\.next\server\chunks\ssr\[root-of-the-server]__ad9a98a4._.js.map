{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/text-effect-flipper.tsx"], "sourcesContent": ["import React, { ReactNode } from \"react\"\nimport { motion } from \"framer-motion\"\n\nconst DURATION = 0.25\nconst STAGGER = 0.025\n\ninterface FlipLinkProps {\n  children: string\n  href: string\n}\n\nconst FlipLink: React.FC<FlipLinkProps> = ({ children, href }) => {\n  return (\n    <motion.a\n      initial=\"initial\"\n      whileHover=\"hovered\"\n      target=\"_blank\"\n      href={href}\n      className=\"relative block overflow-hidden whitespace-nowrap text-4xl font-semibold uppercase dark:text-white/90 sm:text-7xl md:text-8xl \"\n      style={{\n        lineHeight: 0.75,\n      }}\n    >\n      <div>\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: 0,\n              },\n              hovered: {\n                y: \"-100%\",\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n      <div className=\"absolute inset-0\">\n        {children.split(\"\").map((l, i) => (\n          <motion.span\n            variants={{\n              initial: {\n                y: \"100%\",\n              },\n              hovered: {\n                y: 0,\n              },\n            }}\n            transition={{\n              duration: DURATION,\n              ease: \"easeInOut\",\n              delay: STAGGER * i,\n            }}\n            className=\"inline-block\"\n            key={i}\n          >\n            {l}\n          </motion.span>\n        ))}\n      </div>\n    </motion.a>\n  )\n}\n\nexport default FlipLink;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,WAAW;AACjB,MAAM,UAAU;AAOhB,MAAM,WAAoC,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE;IAC3D,qBACE,8OAAC,wJAAA,CAAA,SAAM,CAAC,CAAC;QACP,SAAQ;QACR,YAAW;QACX,QAAO;QACP,MAAM;QACN,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAEA,8OAAC;0BACE,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,wJAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;0BAMX,8OAAC;gBAAI,WAAU;0BACZ,SAAS,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,kBAC1B,8OAAC,wJAAA,CAAA,SAAM,CAAC,IAAI;wBACV,UAAU;4BACR,SAAS;gCACP,GAAG;4BACL;4BACA,SAAS;gCACP,GAAG;4BACL;wBACF;wBACA,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,OAAO,UAAU;wBACnB;wBACA,WAAU;kCAGT;uBAFI;;;;;;;;;;;;;;;;AAQjB;uCAEe", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/components/ui/homePage.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport FlipLink from \"./text-effect-flipper\";\r\n\r\nconst HomePage = () => {\r\n  return (\r\n    <h1>\r\n      <FlipLink href=\"hello.com\"><PERSON><PERSON></FlipLink>\r\n    </h1>\r\n  );\r\n};\r\n\r\nexport default HomePage;\r\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,WAAW;IACf,qBACE,8OAAC;kBACC,cAAA,8OAAC,qJAAA,CAAA,UAAQ;YAAC,MAAK;sBAAY;;;;;;;;;;;AAGjC;uCAEe", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Development/My%20Projects/Portfolio/my-app/src/app/page.tsx"], "sourcesContent": ["import HomePage from \"@/components/ui/homePage\";\nimport React from \"react\";\n\nexport default function page() {\n  return (\n    <div>\n      <h1 className=\"text-white-500 text-7xl\">Hello Everyone</h1>\n      <HomePage/>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAGe,SAAS;IACtB,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC,oIAAA,CAAA,UAAQ;;;;;;;;;;;AAGf", "debugId": null}}]}